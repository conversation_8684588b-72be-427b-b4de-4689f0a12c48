{"cells": [{"cell_type": "code", "execution_count": 142, "id": "afa7ffeb-735e-41da-9c0b-d12e630fac01", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "from datetime import datetime, timedelta\n", "import random\n", "\n", "random.seed(42)\n", "data_path = \"../../Data/N-BaIoT/\""]}, {"cell_type": "code", "execution_count": 143, "id": "b0f19100-3c7d-4efa-acec-7aeaaad8126b", "metadata": {}, "outputs": [{"data": {"text/plain": ["['IID-10-Client_Data',\n", " 'notused-10-<PERSON>lient_Data',\n", " 'Client_Data',\n", " 'Provision_PT_838_Security_Camera',\n", " 'IID-50-Client_Data',\n", " 'old-device-exp-Client_Data',\n", " 'Samsung_SNH_1011_N_Webcam',\n", " 'nonIID-20_Client_Data',\n", " 'SimpleHome_XCS7_1003_WHT_Security_Camera',\n", " 'noniid-10-Client_Data',\n", " 'SimpleHome_XCS7_1002_WHT_Security_Camera',\n", " 'Provision_PT_737E_Security_Camera',\n", " 'nonIID-50-Client_Data',\n", " 'nonIID-15_Client_Data',\n", " 'demonstrate_structure.csv',\n", " 'Preliminary',\n", " 'Philips_B120N10_Baby_Monitor',\n", " 'Ecobee_Thermostat',\n", " 'N_BaIoT_dataset_description_v1.txt',\n", " '<PERSON><PERSON>_Doorbell',\n", " '<PERSON><PERSON>_Doorbell']"]}, "execution_count": 143, "metadata": {}, "output_type": "execute_result"}], "source": ["devices_list = os.listdir(data_path)\n", "devices_list"]}, {"cell_type": "code", "execution_count": 144, "id": "45b187d3-9ae6-420b-a887-695cd4864416", "metadata": {}, "outputs": [], "source": ["normal_path_lists = []\n", "abnormal_path_lists = []\n", "for device_name in devices_list:\n", "    normal_path = os.path.join(data_path, device_name,\"normal\")\n", "    abnormal_path = os.path.join(data_path, device_name,\"abnormal\")\n", "    if os.path.isdir(normal_path):\n", "        for filename in os.listdir(normal_path):\n", "            if \"benign\" in filename:\n", "                normal_file = os.path.join(normal_path, filename)\n", "                normal_path_lists.append((device_name, normal_file))\n", "    if os.path.isdir(abnormal_path):\n", "        for filename in os.listdir(abnormal_path):\n", "            if \"mirai\" in filename or \"gafgyt\" in filename:\n", "                abnormal_file = os.path.join(abnormal_path, filename)\n", "                abnormal_path_lists.append((device_name, abnormal_file))\n"]}, {"cell_type": "code", "execution_count": 145, "id": "448af84c-d140-491a-851f-760a2207eb8c", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["[('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/gafgyt_combo.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/mirai_udp.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/gafgyt_tcp.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/gafgyt_junk.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/mirai_syn.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/mirai_scan.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/mirai_ack.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/mirai_udpplain.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/gafgyt_scan.csv'),\n", " ('Provision_PT_838_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_838_Security_Camera/abnormal/gafgyt_udp.csv'),\n", " ('Samsung_SNH_1011_N_Webcam',\n", "  '../../Data/N-BaIoT/Samsung_SNH_1011_N_Webcam/abnormal/gafgyt_combo.csv'),\n", " ('Samsung_SNH_1011_N_Webcam',\n", "  '../../Data/N-BaIoT/Samsung_SNH_1011_N_Webcam/abnormal/gafgyt_tcp.csv'),\n", " ('Samsung_SNH_1011_N_Webcam',\n", "  '../../Data/N-BaIoT/Samsung_SNH_1011_N_Webcam/abnormal/gafgyt_junk.csv'),\n", " ('Samsung_SNH_1011_N_Webcam',\n", "  '../../Data/N-BaIoT/Samsung_SNH_1011_N_Webcam/abnormal/gafgyt_scan.csv'),\n", " ('Samsung_SNH_1011_N_Webcam',\n", "  '../../Data/N-BaIoT/Samsung_SNH_1011_N_Webcam/abnormal/gafgyt_udp.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/gafgyt_combo.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/mirai_udp.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/gafgyt_tcp.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/gafgyt_junk.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/mirai_syn.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/mirai_scan.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/mirai_ack.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/mirai_udpplain.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/gafgyt_scan.csv'),\n", " ('SimpleHome_XCS7_1003_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1003_WHT_Security_Camera/abnormal/gafgyt_udp.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/gafgyt_combo.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/mirai_udp.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/gafgyt_tcp.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/gafgyt_junk.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/mirai_syn.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/mirai_scan.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/mirai_ack.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/mirai_udpplain.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/gafgyt_scan.csv'),\n", " ('SimpleHome_XCS7_1002_WHT_Security_Camera',\n", "  '../../Data/N-BaIoT/SimpleHome_XCS7_1002_WHT_Security_Camera/abnormal/gafgyt_udp.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/gafgyt_combo.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/mirai_udp.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/gafgyt_tcp.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/gafgyt_junk.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/mirai_syn.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/mirai_scan.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/mirai_ack.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/mirai_udpplain.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/gafgyt_scan.csv'),\n", " ('Provision_PT_737E_Security_Camera',\n", "  '../../Data/N-BaIoT/Provision_PT_737E_Security_Camera/abnormal/gafgyt_udp.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/gafgyt_combo.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/mirai_udp.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/gafgyt_tcp.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/gafgyt_junk.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/mirai_syn.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/mirai_scan.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/mirai_ack.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/mirai_udpplain.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/gafgyt_scan.csv'),\n", " ('Philips_B120N10_Baby_Monitor',\n", "  '../../Data/N-BaIoT/Philips_B120N10_Baby_Monitor/abnormal/gafgyt_udp.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/gafgyt_combo.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/mirai_udp.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/gafgyt_tcp.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/gafgyt_junk.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/mirai_syn.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/mirai_scan.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/mirai_ack.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/mirai_udpplain.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/gafgyt_scan.csv'),\n", " ('Ecobee_Thermostat',\n", "  '../../Data/N-BaIoT/Ecobee_Thermostat/abnormal/gafgyt_udp.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Ennio_Doorbell/abnormal/gafgyt_combo.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Ennio_Doorbell/abnormal/gafgyt_tcp.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Ennio_Doorbell/abnormal/gafgyt_junk.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Ennio_Doorbell/abnormal/gafgyt_scan.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Ennio_Doorbell/abnormal/gafgyt_udp.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/gafgyt_combo.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/mirai_udp.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/gafgyt_tcp.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/gafgyt_junk.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/mirai_syn.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/mirai_scan.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/mirai_ack.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/mirai_udpplain.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/gafgyt_scan.csv'),\n", " ('<PERSON><PERSON>_<PERSON>bell',\n", "  '../../Data/N-BaIoT/Dan<PERSON>_Doorbell/abnormal/gafgyt_udp.csv')]"]}, "execution_count": 145, "metadata": {}, "output_type": "execute_result"}], "source": ["abnormal_path_lists"]}, {"cell_type": "code", "execution_count": 146, "id": "1f74fcd1-f796-46c8-8fb7-9b071eb9435f", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MI_dir_L5_weight</th>\n", "      <th>MI_dir_L5_mean</th>\n", "      <th>MI_dir_L5_variance</th>\n", "      <th>MI_dir_L3_weight</th>\n", "      <th>MI_dir_L3_mean</th>\n", "      <th>MI_dir_L3_variance</th>\n", "      <th>MI_dir_L1_weight</th>\n", "      <th>MI_dir_L1_mean</th>\n", "      <th>MI_dir_L1_variance</th>\n", "      <th>MI_dir_L0.1_weight</th>\n", "      <th>...</th>\n", "      <th>HpHp_L0.1_radius</th>\n", "      <th>HpHp_L0.1_covariance</th>\n", "      <th>HpHp_L0.1_pcc</th>\n", "      <th>HpHp_L0.01_weight</th>\n", "      <th>HpHp_L0.01_mean</th>\n", "      <th>HpHp_L0.01_std</th>\n", "      <th>HpHp_L0.01_magnitude</th>\n", "      <th>HpHp_L0.01_radius</th>\n", "      <th>HpHp_L0.01_covariance</th>\n", "      <th>HpHp_L0.01_pcc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.000000</td>\n", "      <td>590.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000</td>\n", "      <td>590.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>590.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>...</td>\n", "      <td>8.323848</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.456899</td>\n", "      <td>443.197258</td>\n", "      <td>121.885335</td>\n", "      <td>443.197258</td>\n", "      <td>14856.03496</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.956973</td>\n", "      <td>590.000000</td>\n", "      <td>5.820000e-11</td>\n", "      <td>1.973957</td>\n", "      <td>590.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.991242</td>\n", "      <td>590.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.999121</td>\n", "      <td>...</td>\n", "      <td>4.160671</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.456507</td>\n", "      <td>470.101420</td>\n", "      <td>123.932152</td>\n", "      <td>470.101420</td>\n", "      <td>15359.17835</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.000000</td>\n", "      <td>60.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.988608</td>\n", "      <td>66.034372</td>\n", "      <td>3.599882e+01</td>\n", "      <td>1.993149</td>\n", "      <td>66.020623</td>\n", "      <td>35.999575</td>\n", "      <td>1.997711</td>\n", "      <td>66.006874</td>\n", "      <td>35.999953</td>\n", "      <td>1.999771</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>72.000000</td>\n", "      <td>0.000000</td>\n", "      <td>72.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1.424801</td>\n", "      <td>82.854706</td>\n", "      <td>1.309192e+02</td>\n", "      <td>1.789444</td>\n", "      <td>79.421082</td>\n", "      <td>157.644192</td>\n", "      <td>2.467101</td>\n", "      <td>75.732106</td>\n", "      <td>160.166516</td>\n", "      <td>2.938978</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1.000090</td>\n", "      <td>78.000438</td>\n", "      <td>1.395266e-02</td>\n", "      <td>1.005419</td>\n", "      <td>78.007660</td>\n", "      <td>0.860559</td>\n", "      <td>1.356942</td>\n", "      <td>77.403433</td>\n", "      <td>43.128694</td>\n", "      <td>3.422353</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>78.000000</td>\n", "      <td>0.000000</td>\n", "      <td>78.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1.517272</td>\n", "      <td>78.000149</td>\n", "      <td>4.756819e-03</td>\n", "      <td>1.676948</td>\n", "      <td>78.003092</td>\n", "      <td>0.347403</td>\n", "      <td>2.189316</td>\n", "      <td>77.675923</td>\n", "      <td>23.517383</td>\n", "      <td>4.377524</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>78.000000</td>\n", "      <td>0.000000</td>\n", "      <td>78.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2.318982</td>\n", "      <td>78.000085</td>\n", "      <td>2.705572e-03</td>\n", "      <td>2.541788</td>\n", "      <td>78.001876</td>\n", "      <td>0.210729</td>\n", "      <td>3.128843</td>\n", "      <td>77.779500</td>\n", "      <td>16.023901</td>\n", "      <td>5.365279</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>78.000000</td>\n", "      <td>0.000000</td>\n", "      <td>78.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 115 columns</p>\n", "</div>"], "text/plain": ["   MI_dir_L5_weight  MI_dir_L5_mean  MI_dir_L5_variance  MI_dir_L3_weight  \\\n", "0          1.000000       60.000000        0.000000e+00          1.000000   \n", "1          1.000000      590.000000        0.000000e+00          1.000000   \n", "2          1.956973      590.000000        5.820000e-11          1.973957   \n", "3          1.000000       60.000000        0.000000e+00          1.000000   \n", "4          1.988608       66.034372        3.599882e+01          1.993149   \n", "5          1.424801       82.854706        1.309192e+02          1.789444   \n", "6          1.000000       90.000000        0.000000e+00          1.000000   \n", "7          1.000090       78.000438        1.395266e-02          1.005419   \n", "8          1.517272       78.000149        4.756819e-03          1.676948   \n", "9          2.318982       78.000085        2.705572e-03          2.541788   \n", "\n", "   MI_dir_L3_mean  MI_dir_L3_variance  MI_dir_L1_weight  MI_dir_L1_mean  \\\n", "0       60.000000            0.000000          1.000000       60.000000   \n", "1      590.000000            0.000000          1.000000      590.000000   \n", "2      590.000000            0.000000          1.991242      590.000000   \n", "3       60.000000            0.000000          1.000000       60.000000   \n", "4       66.020623           35.999575          1.997711       66.006874   \n", "5       79.421082          157.644192          2.467101       75.732106   \n", "6       90.000000            0.000000          1.000000       90.000000   \n", "7       78.007660            0.860559          1.356942       77.403433   \n", "8       78.003092            0.347403          2.189316       77.675923   \n", "9       78.001876            0.210729          3.128843       77.779500   \n", "\n", "   MI_dir_L1_variance  MI_dir_L0.1_weight  ...  HpHp_L0.1_radius  \\\n", "0            0.000000            1.000000  ...          0.000000   \n", "1            0.000000            1.000000  ...          8.323848   \n", "2            0.000000            1.999121  ...          4.160671   \n", "3            0.000000            1.000000  ...          0.000000   \n", "4           35.999953            1.999771  ...          0.000000   \n", "5          160.166516            2.938978  ...          0.000000   \n", "6            0.000000            1.000000  ...          0.000000   \n", "7           43.128694            3.422353  ...          0.000000   \n", "8           23.517383            4.377524  ...          0.000000   \n", "9           16.023901            5.365279  ...          0.000000   \n", "\n", "   HpHp_L0.1_covariance  HpHp_L0.1_pcc  HpHp_L0.01_weight  HpHp_L0.01_mean  \\\n", "0                   0.0            0.0           1.000000        60.000000   \n", "1                   0.0            0.0           4.456899       443.197258   \n", "2                   0.0            0.0           5.456507       470.101420   \n", "3                   0.0            0.0           1.000000        60.000000   \n", "4                   0.0            0.0           1.000000        72.000000   \n", "5                   0.0            0.0           1.000000        90.000000   \n", "6                   0.0            0.0           1.000000        90.000000   \n", "7                   0.0            0.0           1.000000        78.000000   \n", "8                   0.0            0.0           1.000000        78.000000   \n", "9                   0.0            0.0           1.000000        78.000000   \n", "\n", "   HpHp_L0.01_std  HpHp_L0.01_magnitude  HpHp_L0.01_radius  \\\n", "0        0.000000             60.000000            0.00000   \n", "1      121.885335            443.197258        14856.03496   \n", "2      123.932152            470.101420        15359.17835   \n", "3        0.000000             60.000000            0.00000   \n", "4        0.000000             72.000000            0.00000   \n", "5        0.000000             90.000000            0.00000   \n", "6        0.000000             90.000000            0.00000   \n", "7        0.000000             78.000000            0.00000   \n", "8        0.000000             78.000000            0.00000   \n", "9        0.000000             78.000000            0.00000   \n", "\n", "   HpHp_L0.01_covariance  HpHp_L0.01_pcc  \n", "0                    0.0             0.0  \n", "1                    0.0             0.0  \n", "2                    0.0             0.0  \n", "3                    0.0             0.0  \n", "4                    0.0             0.0  \n", "5                    0.0             0.0  \n", "6                    0.0             0.0  \n", "7                    0.0             0.0  \n", "8                    0.0             0.0  \n", "9                    0.0             0.0  \n", "\n", "[10 rows x 115 columns]"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(normal_path_lists[0][1])\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": 147, "id": "ce8c7860-9c1b-49ae-8ee2-a7ebcd7d6b1c", "metadata": {}, "outputs": [], "source": ["all_df = []\n", "# randomly take sample of each device\n", "percentage_used_normal_data = 0.05\n", "# percentage_used_normal_data = 0.15\n", "for device_name, data_file in normal_path_lists:\n", "    normal_df = pd.read_csv(data_file)\n", "    normal_df = normal_df.sample(int(percentage_used_normal_data*normal_df.shape[0]))\n", "    normal_df['Label'] = 0\n", "    normal_df['Device'] = device_name\n", "    all_df.append(normal_df)\n", "\n", "percentage_used_abnormal_data = 0.005\n", "# percentage_used_abnormal_data = 0.05\n", "for device_name, data_file in abnormal_path_lists:\n", "    abnormal_df = pd.read_csv(data_file)\n", "    abnormal_df = abnormal_df.sample(int(percentage_used_abnormal_data*abnormal_df.shape[0]))\n", "    abnormal_df['Label'] = 1\n", "    abnormal_df['Device'] = device_name\n", "    all_df.append(abnormal_df)\n", "\n", "full_data = pd.concat(all_df, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 148, "id": "9d28573e-97df-46af-ba0d-9e35bca79a3f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Philips_B120N10_Baby_Monitor                8762\n", "Provision_PT_838_Security_Camera            4925\n", "Provision_PT_737E_Security_Camera           3107\n", "Samsung_SNH_1011_N_Webcam                   2607\n", "<PERSON><PERSON>_Doorbell                            2477\n", "SimpleHome_XCS7_1002_WHT_Security_Camera    2329\n", "<PERSON><PERSON><PERSON>                              1955\n", "SimpleHome_XCS7_1003_WHT_Security_Camera     976\n", "Ecobee_Thermostat                            655\n", "Name: <PERSON><PERSON>, dtype: int64"]}, "execution_count": 148, "metadata": {}, "output_type": "execute_result"}], "source": ["full_data[full_data['Label'] == 0]['Device'].value_counts()"]}, {"cell_type": "code", "execution_count": 149, "id": "12574526-61d1-4dfd-9f8d-a23cbd68c829", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON><PERSON>_Doorbell                            4838\n", "Philips_B120N10_Baby_Monitor                4612\n", "SimpleHome_XCS7_1003_WHT_Security_Camera    4151\n", "Ecobee_Thermostat                           4109\n", "SimpleHome_XCS7_1002_WHT_Security_Camera    4078\n", "Provision_PT_737E_Security_Camera           3825\n", "Provision_PT_838_Security_Camera            3686\n", "Samsung_SNH_1011_N_Webcam                   1613\n", "<PERSON><PERSON>_<PERSON>bell                              1579\n", "Name: <PERSON><PERSON>, dtype: int64"]}, "execution_count": 149, "metadata": {}, "output_type": "execute_result"}], "source": ["full_data[full_data['Label'] == 1]['Device'].value_counts()"]}, {"cell_type": "code", "execution_count": 150, "id": "fb5b2251-3d27-4952-84ba-95929af39fbe", "metadata": {}, "outputs": [{"data": {"text/plain": ["27793"]}, "execution_count": 150, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(full_data[full_data['Label'] == 0]['Device'].value_counts())"]}, {"cell_type": "code", "execution_count": 151, "id": "daa50012-4b6c-461d-ae47-51ff1c354951", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MI_dir_L5_weight</th>\n", "      <th>MI_dir_L5_mean</th>\n", "      <th>MI_dir_L5_variance</th>\n", "      <th>MI_dir_L3_weight</th>\n", "      <th>MI_dir_L3_mean</th>\n", "      <th>MI_dir_L3_variance</th>\n", "      <th>MI_dir_L1_weight</th>\n", "      <th>MI_dir_L1_mean</th>\n", "      <th>MI_dir_L1_variance</th>\n", "      <th>MI_dir_L0.1_weight</th>\n", "      <th>...</th>\n", "      <th>HpHp_L0.1_pcc</th>\n", "      <th>HpHp_L0.01_weight</th>\n", "      <th>HpHp_L0.01_mean</th>\n", "      <th>HpHp_L0.01_std</th>\n", "      <th>HpHp_L0.01_magnitude</th>\n", "      <th>HpHp_L0.01_radius</th>\n", "      <th>HpHp_L0.01_covariance</th>\n", "      <th>HpHp_L0.01_pcc</th>\n", "      <th>Label</th>\n", "      <th>Device</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>13.949732</td>\n", "      <td>154.156893</td>\n", "      <td>4.749216e+04</td>\n", "      <td>20.041018</td>\n", "      <td>154.099334</td>\n", "      <td>4.867405e+04</td>\n", "      <td>29.740919</td>\n", "      <td>152.030480</td>\n", "      <td>4.853966e+04</td>\n", "      <td>57.305619</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000</td>\n", "      <td>179.000000</td>\n", "      <td>0.000000</td>\n", "      <td>179.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.487120</td>\n", "      <td>77.937854</td>\n", "      <td>2.163690e+02</td>\n", "      <td>2.969077</td>\n", "      <td>79.895685</td>\n", "      <td>2.010300e+02</td>\n", "      <td>3.648910</td>\n", "      <td>81.565298</td>\n", "      <td>1.798128e+02</td>\n", "      <td>8.834063</td>\n", "      <td>...</td>\n", "      <td>7.820000e-17</td>\n", "      <td>6.504240</td>\n", "      <td>60.000000</td>\n", "      <td>0.000001</td>\n", "      <td>84.852814</td>\n", "      <td>1.930000e-12</td>\n", "      <td>5.540000e-30</td>\n", "      <td>4.060000e-18</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.062894</td>\n", "      <td>74.000000</td>\n", "      <td>2.510000e-09</td>\n", "      <td>1.253319</td>\n", "      <td>73.999997</td>\n", "      <td>2.984870e-04</td>\n", "      <td>2.298232</td>\n", "      <td>74.635310</td>\n", "      <td>4.645185e+02</td>\n", "      <td>30.354968</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.993116</td>\n", "      <td>74.000000</td>\n", "      <td>0.000000</td>\n", "      <td>74.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.092464</td>\n", "      <td>73.084638</td>\n", "      <td>7.747470e-02</td>\n", "      <td>1.288793</td>\n", "      <td>73.224080</td>\n", "      <td>1.738682e-01</td>\n", "      <td>2.226995</td>\n", "      <td>73.550952</td>\n", "      <td>2.475984e-01</td>\n", "      <td>5.698445</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000</td>\n", "      <td>73.000000</td>\n", "      <td>0.000000</td>\n", "      <td>73.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2.999945</td>\n", "      <td>90.000000</td>\n", "      <td>9.090000e-13</td>\n", "      <td>2.999967</td>\n", "      <td>90.000000</td>\n", "      <td>1.820000e-12</td>\n", "      <td>2.999989</td>\n", "      <td>90.000000</td>\n", "      <td>2.460000e-11</td>\n", "      <td>3.282650</td>\n", "      <td>...</td>\n", "      <td>-1.450366e-03</td>\n", "      <td>5.168731</td>\n", "      <td>86.551021</td>\n", "      <td>9.569426</td>\n", "      <td>105.476805</td>\n", "      <td>9.157522e+01</td>\n", "      <td>-2.516210e-01</td>\n", "      <td>-3.761208e-02</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1.973538</td>\n", "      <td>73.852775</td>\n", "      <td>2.039477e+00</td>\n", "      <td>2.069184</td>\n", "      <td>73.338451</td>\n", "      <td>8.825249e+00</td>\n", "      <td>2.487133</td>\n", "      <td>71.638588</td>\n", "      <td>3.443946e+01</td>\n", "      <td>6.153726</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000</td>\n", "      <td>74.000000</td>\n", "      <td>0.000000</td>\n", "      <td>74.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1.116898</td>\n", "      <td>74.389463</td>\n", "      <td>1.446963e+02</td>\n", "      <td>1.534754</td>\n", "      <td>76.847836</td>\n", "      <td>6.417072e+02</td>\n", "      <td>3.734924</td>\n", "      <td>82.779565</td>\n", "      <td>1.580884e+03</td>\n", "      <td>10.181818</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000</td>\n", "      <td>74.000000</td>\n", "      <td>0.000000</td>\n", "      <td>74.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3.000000</td>\n", "      <td>89.999755</td>\n", "      <td>7.361799e-03</td>\n", "      <td>3.005289</td>\n", "      <td>89.973454</td>\n", "      <td>7.956829e-01</td>\n", "      <td>3.576366</td>\n", "      <td>87.582594</td>\n", "      <td>6.667834e+01</td>\n", "      <td>8.434828</td>\n", "      <td>...</td>\n", "      <td>5.657884e-01</td>\n", "      <td>6.187700</td>\n", "      <td>84.788691</td>\n", "      <td>11.365805</td>\n", "      <td>104.110645</td>\n", "      <td>1.291832e+02</td>\n", "      <td>3.108256e+00</td>\n", "      <td>3.373612e-01</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1.017213</td>\n", "      <td>89.729246</td>\n", "      <td>4.258752e+00</td>\n", "      <td>1.105199</td>\n", "      <td>88.477034</td>\n", "      <td>2.204803e+01</td>\n", "      <td>1.875559</td>\n", "      <td>82.530791</td>\n", "      <td>6.371826e+01</td>\n", "      <td>4.338490</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>5.709990</td>\n", "      <td>85.742333</td>\n", "      <td>10.469111</td>\n", "      <td>85.742333</td>\n", "      <td>1.096023e+02</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>13.671830</td>\n", "      <td>113.536090</td>\n", "      <td>2.065194e+04</td>\n", "      <td>14.296040</td>\n", "      <td>113.127727</td>\n", "      <td>2.046425e+04</td>\n", "      <td>15.683316</td>\n", "      <td>110.258219</td>\n", "      <td>1.945236e+04</td>\n", "      <td>37.129677</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.999961</td>\n", "      <td>69.999921</td>\n", "      <td>4.000000</td>\n", "      <td>101.862599</td>\n", "      <td>1.600000e+01</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0</td>\n", "      <td>Provision_PT_838_Security_Camera</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 117 columns</p>\n", "</div>"], "text/plain": ["   MI_dir_L5_weight  MI_dir_L5_mean  MI_dir_L5_variance  MI_dir_L3_weight  \\\n", "0         13.949732      154.156893        4.749216e+04         20.041018   \n", "1          2.487120       77.937854        2.163690e+02          2.969077   \n", "2          1.062894       74.000000        2.510000e-09          1.253319   \n", "3          1.092464       73.084638        7.747470e-02          1.288793   \n", "4          2.999945       90.000000        9.090000e-13          2.999967   \n", "5          1.973538       73.852775        2.039477e+00          2.069184   \n", "6          1.116898       74.389463        1.446963e+02          1.534754   \n", "7          3.000000       89.999755        7.361799e-03          3.005289   \n", "8          1.017213       89.729246        4.258752e+00          1.105199   \n", "9         13.671830      113.536090        2.065194e+04         14.296040   \n", "\n", "   MI_dir_L3_mean  MI_dir_L3_variance  MI_dir_L1_weight  MI_dir_L1_mean  \\\n", "0      154.099334        4.867405e+04         29.740919      152.030480   \n", "1       79.895685        2.010300e+02          3.648910       81.565298   \n", "2       73.999997        2.984870e-04          2.298232       74.635310   \n", "3       73.224080        1.738682e-01          2.226995       73.550952   \n", "4       90.000000        1.820000e-12          2.999989       90.000000   \n", "5       73.338451        8.825249e+00          2.487133       71.638588   \n", "6       76.847836        6.417072e+02          3.734924       82.779565   \n", "7       89.973454        7.956829e-01          3.576366       87.582594   \n", "8       88.477034        2.204803e+01          1.875559       82.530791   \n", "9      113.127727        2.046425e+04         15.683316      110.258219   \n", "\n", "   MI_dir_L1_variance  MI_dir_L0.1_weight  ...  HpHp_L0.1_pcc  \\\n", "0        4.853966e+04           57.305619  ...   0.000000e+00   \n", "1        1.798128e+02            8.834063  ...   7.820000e-17   \n", "2        4.645185e+02           30.354968  ...   0.000000e+00   \n", "3        2.475984e-01            5.698445  ...   0.000000e+00   \n", "4        2.460000e-11            3.282650  ...  -1.450366e-03   \n", "5        3.443946e+01            6.153726  ...   0.000000e+00   \n", "6        1.580884e+03           10.181818  ...   0.000000e+00   \n", "7        6.667834e+01            8.434828  ...   5.657884e-01   \n", "8        6.371826e+01            4.338490  ...   0.000000e+00   \n", "9        1.945236e+04           37.129677  ...   0.000000e+00   \n", "\n", "   HpHp_L0.01_weight  HpHp_L0.01_mean  HpHp_L0.01_std  HpHp_L0.01_magnitude  \\\n", "0           1.000000       179.000000        0.000000            179.000000   \n", "1           6.504240        60.000000        0.000001             84.852814   \n", "2           1.993116        74.000000        0.000000             74.000000   \n", "3           1.000000        73.000000        0.000000             73.000000   \n", "4           5.168731        86.551021        9.569426            105.476805   \n", "5           1.000000        74.000000        0.000000             74.000000   \n", "6           1.000000        74.000000        0.000000             74.000000   \n", "7           6.187700        84.788691       11.365805            104.110645   \n", "8           5.709990        85.742333       10.469111             85.742333   \n", "9           1.999961        69.999921        4.000000            101.862599   \n", "\n", "   HpHp_L0.01_radius  HpHp_L0.01_covariance  HpHp_L0.01_pcc  Label  \\\n", "0       0.000000e+00           0.000000e+00    0.000000e+00      0   \n", "1       1.930000e-12           5.540000e-30    4.060000e-18      0   \n", "2       0.000000e+00           0.000000e+00    0.000000e+00      0   \n", "3       0.000000e+00           0.000000e+00    0.000000e+00      0   \n", "4       9.157522e+01          -2.516210e-01   -3.761208e-02      0   \n", "5       0.000000e+00           0.000000e+00    0.000000e+00      0   \n", "6       0.000000e+00           0.000000e+00    0.000000e+00      0   \n", "7       1.291832e+02           3.108256e+00    3.373612e-01      0   \n", "8       1.096023e+02           0.000000e+00    0.000000e+00      0   \n", "9       1.600000e+01           0.000000e+00    0.000000e+00      0   \n", "\n", "                             <PERSON><PERSON>  \n", "0  Provision_PT_838_Security_Camera  \n", "1  Provision_PT_838_Security_Camera  \n", "2  Provision_PT_838_Security_Camera  \n", "3  Provision_PT_838_Security_Camera  \n", "4  Provision_PT_838_Security_Camera  \n", "5  Provision_PT_838_Security_Camera  \n", "6  Provision_PT_838_Security_Camera  \n", "7  Provision_PT_838_Security_Camera  \n", "8  Provision_PT_838_Security_Camera  \n", "9  Provision_PT_838_Security_Camera  \n", "\n", "[10 rows x 117 columns]"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}], "source": ["full_data.head(10)"]}, {"cell_type": "code", "execution_count": 152, "id": "5a6bbf44-df77-45af-b944-a76743b9a467", "metadata": {}, "outputs": [{"data": {"text/plain": ["(27793, 115)"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["normal_data = full_data[full_data['Label'] == 0].iloc[:,:-1]\n", "normal_data = normal_data.drop(['Label'], axis=1)\n", "normal_device_label = full_data[full_data['Label'] == 0].iloc[:,-1]\n", "normal_data.shape"]}, {"cell_type": "code", "execution_count": 153, "id": "d076659d-ff0c-4dab-8c4e-9b6eacc08d7e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(32491, 115)"]}, "execution_count": 153, "metadata": {}, "output_type": "execute_result"}], "source": ["abnormal_data = full_data[full_data['Label'] == 1].iloc[:,:-1]\n", "abnormal_data = abnormal_data.drop(['Label'], axis=1)\n", "abnormal_device_label = full_data[full_data['Label'] == 1].iloc[:,-1]\n", "abnormal_data.shape"]}, {"cell_type": "code", "execution_count": 154, "id": "3c4460a0-1ad6-4a94-9411-ef0d02e27cb1", "metadata": {"scrolled": true}, "outputs": [], "source": ["normal_device_mapping = {\n", "    v: k for k, v in enumerate(normal_device_label.unique())\n", "}\n", "abnormal_device_mapping = {\n", "    v: k for k, v in enumerate(abnormal_device_label.unique())\n", "}\n", "encoded_abnormal_device = abnormal_device_label.map(abnormal_device_mapping)\n", "encoded_normal_device = normal_device_label.map(normal_device_mapping)"]}, {"cell_type": "code", "execution_count": 155, "id": "f0e9ae4c-d06b-459a-8cfc-962f7cad4efa", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Provision_PT_838_Security_Camera': 0,\n", " 'Samsung_SNH_1011_N_Webcam': 1,\n", " 'SimpleHome_XCS7_1003_WHT_Security_Camera': 2,\n", " 'SimpleHome_XCS7_1002_WHT_Security_Camera': 3,\n", " 'Provision_PT_737E_Security_Camera': 4,\n", " 'Philips_B120N10_Baby_Monitor': 5,\n", " 'Ecobee_Thermostat': 6,\n", " 'En<PERSON>_Doorbell': 7,\n", " '<PERSON><PERSON>_Doorbell': 8}"]}, "execution_count": 155, "metadata": {}, "output_type": "execute_result"}], "source": ["normal_device_mapping"]}, {"cell_type": "code", "execution_count": 156, "id": "17a31fe6-17fd-4f00-aeb6-75c929ea53b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["1        0\n", "2        0\n", "4        0\n", "5        0\n", "8        0\n", "        ..\n", "27786    8\n", "27787    8\n", "27789    8\n", "27791    8\n", "27792    8\n", "Name: <PERSON><PERSON>, Length: 16676, dtype: int64"]}, "execution_count": 156, "metadata": {}, "output_type": "execute_result"}], "source": ["test_normal_data = normal_data.sample(n = int(0.4 * normal_data.shape[0]), random_state=42)\n", "test_normal_label = encoded_normal_device.iloc[test_normal_data.index]\n", "\n", "normal_data = normal_data.drop(test_normal_data.index)\n", "encoded_normal_device = encoded_normal_device.drop(test_normal_label.index)\n", "# normal_data\n", "encoded_normal_device"]}, {"cell_type": "code", "execution_count": 157, "id": "f120940d-721e-4920-ae47-ff74163f1a7e", "metadata": {}, "outputs": [], "source": ["# -------------------------------------------------------------------------------------------------------\n", "# SPLIT DATA AMONG CLIENTS\n", "# -------------------------------------------------------------------------------------------------------\n", "def split_data(data, labels, n_clients=10, classes_per_client=10, shuffle=True, verbose=True, balancedness=None):\n", "    '''\n", "    Splits (data, labels) evenly among 'n_clients s.t. every client holds 'classes_per_client\n", "    different labels\n", "    data : [n_data x shape]\n", "    labels : [n_data (x 1)] from 0 to n_labels\n", "    '''\n", "    # constants\n", "    n_data = data.shape[0]\n", "    n_labels = np.max(labels) + 1\n", "\n", "    if balancedness >= 1.0:\n", "        data_per_client = [n_data // n_clients]*n_clients\n", "        data_per_client_per_class = [\n", "            data_per_client[0] // classes_per_client]*n_clients\n", "    else:\n", "        fracs = balancedness**np.linspace(0, n_clients-1, n_clients)\n", "        fracs /= np.sum(fracs)\n", "        fracs = 0.1/n_clients + (1-0.1)*fracs\n", "        data_per_client = [np.floor(frac*n_data).astype('int')\n", "                           for frac in fracs]\n", "\n", "        data_per_client = data_per_client[::-1]\n", "\n", "        data_per_client_per_class = [np.maximum(\n", "            1, nd // classes_per_client) for nd in data_per_client]\n", "\n", "    if sum(data_per_client) > n_data:\n", "        print(\"Impossible Split\")\n", "        exit()\n", "\n", "    # sort for labels\n", "    data_idcs = [[] for i in range(n_labels)]\n", "    for j, label in enumerate(labels):\n", "        data_idcs[label] += [j]\n", "    if shuffle:\n", "        for idcs in data_idcs:\n", "            np.random.shuffle(idcs)\n", "\n", "    # split data among clients\n", "    clients_split = []\n", "    c = 0\n", "    for i in range(n_clients):\n", "        client_idcs = []\n", "        budget = data_per_client[i]\n", "        c = np.random.randint(n_labels)\n", "        while budget > 0:\n", "            take = min(data_per_client_per_class[i], len(data_idcs[c]), budget)\n", "\n", "            client_idcs += data_idcs[c][:take]\n", "            data_idcs[c] = data_idcs[c][take:]\n", "\n", "            budget -= take\n", "            c = (c + 1) % n_labels\n", "\n", "        clients_split += [(data[client_idcs], labels[client_idcs])]\n", "\n", "    def print_split(clients_split):\n", "        print(\"Data split:\")\n", "        for i, client in enumerate(clients_split):\n", "            split = np.sum(client[1].reshape(\n", "                1, -1) == np.arange(n_labels).reshape(-1, 1), axis=1)\n", "            print(\" - Client {}: {}\".format(i, split))\n", "        print()\n", "        # You can access the split data for each client\n", "        for i, client in enumerate(clients_split):\n", "            client_data, client_labels = client\n", "            print(\n", "                f\"Client {i} - Data shape: {client_data.shape}, Labels: {np.unique(client_labels)}\")\n", "\n", "    if verbose:\n", "        print_split(clients_split)\n", "\n", "    return clients_split\n"]}, {"cell_type": "code", "execution_count": 158, "id": "1fa8eef9-d281-4c39-8ccc-3fc5931665bc", "metadata": {"scrolled": true}, "outputs": [], "source": ["# normal_split = split_data(normal_data.values, encoded_normal_device.values, n_clients=10, \n", "#           classes_per_client=3, balancedness=0.9, verbose=True)"]}, {"cell_type": "code", "execution_count": 126, "id": "e6314220-ab76-4e75-91aa-497efffd49da", "metadata": {}, "outputs": [], "source": ["# normal_split = split_data(normal_data.values, encoded_normal_device.values, n_clients=10, \n", "#           classes_per_client=3, balancedness=0.8, verbose=True)"]}, {"cell_type": "code", "execution_count": 127, "id": "01a9cf05-c29d-422e-97de-d0055949f1da", "metadata": {}, "outputs": [], "source": ["# client_labels = [\n", "#     [0, 2, 4, 5, 6, 8], [0,4,3,1], \n", "# [0,1,5,7,4,3], [5,3,0,1,2], [1,7,2,5,4,3,6]]"]}, {"cell_type": "code", "execution_count": 128, "id": "e60f420e-0312-4e4a-8a73-81aa94cf3873", "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# import pandas as pd\n", "\n", "# def split_data_with_fixed_label_percentage(data, labels, label_distribution, label_percentage=0.05):\n", "#     '''\n", "#     Splits the dataset into sub-datasets based on a given label distribution.\n", "#     Each label type in each sub-dataset constitutes a fixed percentage of the total data samples.\n", "\n", "#     data: DataFrame or 2D array-like with data samples\n", "#     labels: Series or array-like with labels\n", "#     label_distribution: List of lists, each sublist contains labels for a specific client\n", "#     label_percentage: Fixed percentage of each label type in each sub-dataset\n", "#     '''\n", "#     if not isinstance(data, pd.DataFrame):\n", "#         data = pd.DataFrame(data)\n", "#     if not isinstance(labels, pd.Series):\n", "#         labels = pd.Series(labels)\n", "\n", "#     if 'label' not in data.columns:\n", "#         data['label'] = labels\n", "\n", "#     # Count total samples per label\n", "#     label_counts = labels.value_counts()\n", "\n", "#     clients_split = []\n", "#     for client_labels in label_distribution:\n", "#         client_data = pd.DataFrame()\n", "\n", "#         for label in client_labels:\n", "#             max_samples = int(np.floor(label_counts[label] * label_percentage))\n", "#             label_data = data[data['label'] == label].head(max_samples)\n", "#             client_data = pd.concat([client_data, label_data])\n", "\n", "#             # Update the main data to remove selected samples\n", "#             data = data.drop(label_data.index)\n", "\n", "#         clients_split.append((client_data.iloc[:,:-1], client_data.iloc[:,-1]))\n", "\n", "#     def print_split(clients_split):\n", "#         print(\"Data split:\")\n", "#         for i, client in enumerate(clients_split):\n", "#             split = np.sum(client[1].values.reshape(\n", "#                 1, -1) == np.arange(label_counts.shape[0]).reshape(-1, 1), axis=1)\n", "#             print(\" - Client {}: {}\".format(i, split))\n", "#         print()\n", "#         # You can access the split data for each client\n", "#         for i, client in enumerate(clients_split):\n", "#             client_data, client_labels = client\n", "#             print(\n", "#                 f\"Client {i} - Data shape: {client_data.shape}, Labels: {np.unique(client_labels)}\")\n", "#     print_split(clients_split)\n", "\n", "#     return clients_split\n", "\n", "# # Example usage (assuming df2 is your dataset and 'device' is the label column)\n", "# label_distribution = [\n", "#     [0, 2, 4, 5, 6, 8], [0,4,3,1], \n", "# [0,1,5,7,4,3], [5,3,0,1,2], [1,7,2,5,4,3,6]]\n", "\n", "# abnormal_split = split_data_with_fixed_label_percentage(abnormal_data, \n", "#                                                         encoded_abnormal_device, label_distribution)\n"]}, {"cell_type": "code", "execution_count": 129, "id": "3cc61ea1-fd51-4ef5-9f11-e1878bba5b85", "metadata": {}, "outputs": [], "source": ["# import os\n", "\n", "# client_data_path = \"../../Data/N-BaIoT/Client_Data\"\n", "# for idx, abnormal in enumerate(abnormal_split):\n", "#     # client_data = np.concatenate([normal[0], abnormal[0]], axis=0)\n", "#     # client_label = np.concatenate([normal[1], abnormal[1]], axis=0)\n", "#     # client = np.concatenate((client_data, client_label.reshape(-1, 1)), axis=1)\n", "\n", "#     abnormal_data_path = os.path.join(client_data_path, f\"Client-{idx+1}\" ,\"abnormal\")\n", "#     if not os.path.exists(abnormal_data_path):\n", "#         os.makedirs(abnormal_data_path)\n", "        \n", "#     np.savetxt(os.path.join(abnormal_data_path, \"data.csv\"), abnormal[0], delimiter=\",\")"]}, {"cell_type": "code", "execution_count": 159, "id": "247fdf16-cbb9-4906-8347-5ca582536045", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d518e5cdb2c34ce589b61fd8a86dae97", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(FloatLogSlider(value=1000.0, description='Alpha', layout=Layout(width='1000px'), max=3.0…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["()"]}, "execution_count": 159, "metadata": {}, "output_type": "execute_result"}], "source": ["from fedartml import InteractivePlots\n", "\n", "import seaborn as sns\n", "sns.set(style=\"darkgrid\")\n", "random_state = 42\n", "# Instanciate InteractivePlots object\n", "my_plot = InteractivePlots(labels = encoded_normal_device.values, random_state = random_state, distance = \"jensen-shannon\")\n", "\n", "# Show plot\n", "my_plot.show_stacked_distr_dirichlet()"]}, {"cell_type": "code", "execution_count": 160, "id": "8688ee3f-bd45-4df3-817a-0ad21ff49438", "metadata": {"scrolled": true}, "outputs": [], "source": ["from fedartml import SplitAsFederatedData\n", "\n", "# Instantiate a SplitAsFederatedData object\n", "my_federater = SplitAsFederatedData(random_state = 42)\n", "\n", "# Get federated dataset from centralized dataset\n", "clients_glob_dic, list_ids_sampled_dic, miss_class_per_node, distances = my_federater.create_clients(image_list = normal_data.values.tolist(), label_list = encoded_normal_device, \n", "                                                             num_clients = 10, prefix_cli='Local_node',\n", "                                                        method = \"dirichlet\", alpha = 1000)\n"]}, {"cell_type": "code", "execution_count": 161, "id": "9269d3e1-167f-4205-969c-86795cbbf2a9", "metadata": {"scrolled": true}, "outputs": [], "source": ["clients_glob = clients_glob_dic['without_class_completion']\n", "list_ids_sampled = list_ids_sampled_dic['without_class_completion']"]}, {"cell_type": "code", "execution_count": 162, "id": "c06782b6-01a8-4814-8dc5-202c3ce17382", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>...</th>\n", "      <th>106</th>\n", "      <th>107</th>\n", "      <th>108</th>\n", "      <th>109</th>\n", "      <th>110</th>\n", "      <th>111</th>\n", "      <th>112</th>\n", "      <th>113</th>\n", "      <th>114</th>\n", "      <th>115</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3.022081</td>\n", "      <td>452.680076</td>\n", "      <td>1811.179971</td>\n", "      <td>3.113429</td>\n", "      <td>441.452810</td>\n", "      <td>6039.771659</td>\n", "      <td>3.515246</td>\n", "      <td>398.639488</td>\n", "      <td>19720.632240</td>\n", "      <td>5.868891</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>15.273211</td>\n", "      <td>456.333696</td>\n", "      <td>19.344699</td>\n", "      <td>456.333696</td>\n", "      <td>3.742174e+02</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>90.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000170</td>\n", "      <td>...</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>1.724573</td>\n", "      <td>90.000000</td>\n", "      <td>0.000001</td>\n", "      <td>90.000000</td>\n", "      <td>1.820000e-12</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.999270</td>\n", "      <td>72.001505</td>\n", "      <td>216.009029</td>\n", "      <td>4.999562</td>\n", "      <td>72.000903</td>\n", "      <td>216.005418</td>\n", "      <td>4.999854</td>\n", "      <td>72.000300</td>\n", "      <td>216.001801</td>\n", "      <td>5.856493</td>\n", "      <td>...</td>\n", "      <td>-9.950000e-10</td>\n", "      <td>-0.000002</td>\n", "      <td>5.333147</td>\n", "      <td>83.799057</td>\n", "      <td>12.148111</td>\n", "      <td>103.100197</td>\n", "      <td>1.475766e+02</td>\n", "      <td>1.111720e-01</td>\n", "      <td>2.653061e-02</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.979925</td>\n", "      <td>329.949664</td>\n", "      <td>11.675360</td>\n", "      <td>1.998351</td>\n", "      <td>328.757156</td>\n", "      <td>286.806869</td>\n", "      <td>2.430062</td>\n", "      <td>288.341583</td>\n", "      <td>7984.759552</td>\n", "      <td>9.775494</td>\n", "      <td>...</td>\n", "      <td>-1.570000e-43</td>\n", "      <td>0.000000</td>\n", "      <td>6.274561</td>\n", "      <td>330.000000</td>\n", "      <td>0.000007</td>\n", "      <td>431.490440</td>\n", "      <td>5.250000e-11</td>\n", "      <td>2.270000e-29</td>\n", "      <td>6.380000e-19</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.000010</td>\n", "      <td>65.999963</td>\n", "      <td>0.000224</td>\n", "      <td>1.001276</td>\n", "      <td>65.995493</td>\n", "      <td>0.027021</td>\n", "      <td>1.180637</td>\n", "      <td>65.537880</td>\n", "      <td>2.560268</td>\n", "      <td>8.736303</td>\n", "      <td>...</td>\n", "      <td>-4.036379e+01</td>\n", "      <td>-0.059445</td>\n", "      <td>21.812036</td>\n", "      <td>102.337677</td>\n", "      <td>44.158398</td>\n", "      <td>128.874959</td>\n", "      <td>2.015614e+03</td>\n", "      <td>-5.994822e+01</td>\n", "      <td>-6.010039e-02</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 116 columns</p>\n", "</div>"], "text/plain": ["        0           1            2         3           4            5    \\\n", "0  3.022081  452.680076  1811.179971  3.113429  441.452810  6039.771659   \n", "1  1.000000   90.000000     0.000000  1.000000   90.000000     0.000000   \n", "2  4.999270   72.001505   216.009029  4.999562   72.000903   216.005418   \n", "3  1.979925  329.949664    11.675360  1.998351  328.757156   286.806869   \n", "4  1.000010   65.999963     0.000224  1.001276   65.995493     0.027021   \n", "\n", "        6           7             8         9    ...           106       107  \\\n", "0  3.515246  398.639488  19720.632240  5.868891  ...  0.000000e+00  0.000000   \n", "1  1.000000   90.000000      0.000000  1.000170  ...  0.000000e+00  0.000000   \n", "2  4.999854   72.000300    216.001801  5.856493  ... -9.950000e-10 -0.000002   \n", "3  2.430062  288.341583   7984.759552  9.775494  ... -1.570000e-43  0.000000   \n", "4  1.180637   65.537880      2.560268  8.736303  ... -4.036379e+01 -0.059445   \n", "\n", "         108         109        110         111           112           113  \\\n", "0  15.273211  456.333696  19.344699  456.333696  3.742174e+02  0.000000e+00   \n", "1   1.724573   90.000000   0.000001   90.000000  1.820000e-12  0.000000e+00   \n", "2   5.333147   83.799057  12.148111  103.100197  1.475766e+02  1.111720e-01   \n", "3   6.274561  330.000000   0.000007  431.490440  5.250000e-11  2.270000e-29   \n", "4  21.812036  102.337677  44.158398  128.874959  2.015614e+03 -5.994822e+01   \n", "\n", "            114  115  \n", "0  0.000000e+00    1  \n", "1  0.000000e+00    0  \n", "2  2.653061e-02    0  \n", "3  6.380000e-19    3  \n", "4 -6.010039e-02    5  \n", "\n", "[5 rows x 116 columns]"]}, "execution_count": 162, "metadata": {}, "output_type": "execute_result"}], "source": ["data = clients_glob['Local_node_5']\n", "data = [list(item[0]) + [item[1]] for item in data]\n", "node_5 = pd.DataFrame(data)\n", "node_5.head(5)"]}, {"cell_type": "code", "execution_count": 163, "id": "cd95d8e7-dc42-40cb-81f4-cb7f94a5ef27", "metadata": {}, "outputs": [], "source": ["clients_glob = clients_glob_dic['without_class_completion']\n", "list_ids_sampled = list_ids_sampled_dic['without_class_completion']"]}, {"cell_type": "code", "execution_count": 164, "id": "1f707f9f-a58b-48e9-989c-203774faadad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1691\n", "1655\n", "1664\n", "1653\n", "1651\n", "1663\n", "1668\n", "1676\n", "1655\n", "1700\n"]}], "source": ["import os\n", "\n", "client_data_path = \"../../Data/N-BaIoT/Client_Data\"\n", "for idx, client in enumerate(clients_glob):\n", "    normal_data_path = os.path.join(client_data_path, f\"Client-{idx+1}\" ,\"normal\")\n", "    if not os.path.exists(normal_data_path):\n", "        os.makedirs(normal_data_path)\n", "    client_data = clients_glob[client]\n", "    client_data = [list(item[0]) + [item[1]] for item in client_data]\n", "    client_data = pd.DataFrame(client_data)\n", "    last_column = client_data.columns[-1]\n", "    \n", "    # Group by the last column and filter out the minority classes\n", "    client_data = client_data.groupby(last_column).filter(lambda x: len(x) >= 10)\n", "    client_data = client_data.iloc[:, :-1]\n", "    print(len(client_data))\n", "    # client_data.to_csv(os.path.join(normal_data_path, \"data.csv\"), index=False, header=False)"]}, {"cell_type": "code", "execution_count": 165, "id": "34b7374c-a792-4180-952d-ed97f4925904", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5    531\n", "0    300\n", "4    200\n", "1    158\n", "8    151\n", "3    142\n", "7    114\n", "2     56\n", "6     39\n", "Name: 115, dtype: int64\n", "5    525\n", "0    299\n", "4    183\n", "1    159\n", "8    142\n", "3    140\n", "7    113\n", "2     53\n", "6     41\n", "Name: 115, dtype: int64\n", "5    535\n", "0    289\n", "4    188\n", "1    160\n", "8    141\n", "3    140\n", "7    118\n", "2     55\n", "6     38\n", "Name: 115, dtype: int64\n", "5    521\n", "0    285\n", "4    185\n", "1    166\n", "8    153\n", "3    138\n", "7    113\n", "2     54\n", "6     38\n", "Name: 115, dtype: int64\n", "5    498\n", "0    304\n", "4    196\n", "1    156\n", "8    153\n", "3    145\n", "7    106\n", "2     55\n", "6     38\n", "Name: 115, dtype: int64\n", "5    508\n", "0    307\n", "4    194\n", "1    154\n", "3    147\n", "8    141\n", "7    118\n", "2     54\n", "6     40\n", "Name: 115, dtype: int64\n", "5    513\n", "0    297\n", "4    184\n", "1    165\n", "8    152\n", "3    141\n", "7    122\n", "2     56\n", "6     38\n", "Name: 115, dtype: int64\n", "5    533\n", "0    293\n", "4    187\n", "8    155\n", "1    149\n", "3    140\n", "7    122\n", "2     55\n", "6     42\n", "Name: 115, dtype: int64\n", "5    536\n", "0    282\n", "4    186\n", "1    159\n", "8    145\n", "3    134\n", "7    121\n", "2     54\n", "6     38\n", "Name: 115, dtype: int64\n", "5    548\n", "0    305\n", "4    177\n", "1    162\n", "8    150\n", "3    141\n", "7    122\n", "2     54\n", "6     41\n", "Name: 115, dtype: int64\n"]}], "source": ["for idx, client in enumerate(clients_glob):\n", "    normal_data_path = os.path.join(client_data_path, f\"Client-{idx+1}\" ,\"normal\")\n", "    client_data = clients_glob[client]\n", "    client_data = [list(item[0]) + [item[1]] for item in client_data]\n", "    client_data = pd.DataFrame(client_data)\n", "    # Get the name of the last column\n", "    last_column = client_data.columns[-1]\n", "    \n", "    # Group by the last column and filter out the minority classes\n", "    client_data = client_data.groupby(last_column).filter(lambda x: len(x) >= 10)\n", "    client_labels = client_data.iloc[:,-1]\n", "    print(client_labels.value_counts())\n"]}, {"cell_type": "code", "execution_count": 175, "id": "0f52c657-e3e4-43c5-bcb2-9f280716ff10", "metadata": {}, "outputs": [], "source": ["from fedartml import SplitAsFederatedData\n", "\n", "# Instantiate a SplitAsFederatedData object\n", "my_federater = SplitAsFederatedData(random_state = 42)\n", "\n", "# Get federated dataset from centralized dataset\n", "abnormal_clients_glob_dic, abnormal_list_ids_sampled_dic, abnormal_miss_class_per_node, abnormal_distances = my_federater.create_clients(image_list = abnormal_data.values.tolist(), label_list = encoded_abnormal_device, \n", "                                                             num_clients = 10, prefix_cli='Local_node',\n", "                                                        method = \"dirichlet\", alpha = 1000)\n"]}, {"cell_type": "code", "execution_count": 176, "id": "70168548-c5c1-4b9b-9857-440cc1b7c8c3", "metadata": {}, "outputs": [], "source": ["clients_glob = abnormal_clients_glob_dic['without_class_completion']\n", "list_ids_sampled = abnormal_list_ids_sampled_dic['without_class_completion']"]}, {"cell_type": "code", "execution_count": 177, "id": "0e0b6dd9-ce73-445a-94a9-e51ccae04a7c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3266\n", "3303\n", "3294\n", "3290\n", "3182\n", "3247\n", "3234\n", "3217\n", "3194\n", "3264\n"]}], "source": ["import os\n", "\n", "client_data_path = \"../../Data/N-BaIoT/Client_Data\"\n", "for idx, client in enumerate(clients_glob):\n", "    abnormal_data_path = os.path.join(client_data_path, f\"Client-{idx+1}\" ,\"abnormal\")\n", "    if not os.path.exists(abnormal_data_path):\n", "        os.makedirs(abnormal_data_path)\n", "    client_data = clients_glob[client]\n", "    client_data = [list(item[0]) + [item[1]] for item in client_data]\n", "    client_data = pd.DataFrame(client_data)\n", "    last_column = client_data.columns[-1]\n", "    \n", "    # Group by the last column and filter out the minority classes\n", "    client_data = client_data.groupby(last_column).filter(lambda x: len(x) >= 10)\n", "    \n", "    client_data = client_data.iloc[:, :-1]\n", "    print(len(client_data))\n", "    # client_data.to_csv(os.path.join(abnormal_data_path, \"data.csv\"), index=False, header=False)"]}, {"cell_type": "code", "execution_count": 178, "id": "a1c60c8d-f58b-401a-bdf4-46cac8a3ba2f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8    486\n", "5    456\n", "3    432\n", "2    417\n", "6    414\n", "4    375\n", "0    367\n", "1    160\n", "7    159\n", "Name: 115, dtype: int64\n", "8    497\n", "5    472\n", "2    431\n", "3    421\n", "6    410\n", "4    376\n", "0    368\n", "1    173\n", "7    155\n", "Name: 115, dtype: int64\n", "5    471\n", "8    470\n", "2    421\n", "6    415\n", "4    414\n", "3    412\n", "0    374\n", "7    161\n", "1    156\n", "Name: 115, dtype: int64\n", "8    480\n", "5    452\n", "6    434\n", "3    422\n", "2    404\n", "4    396\n", "0    380\n", "7    164\n", "1    158\n", "Name: 115, dtype: int64\n", "8    493\n", "5    450\n", "2    402\n", "3    395\n", "6    388\n", "4    374\n", "0    367\n", "7    158\n", "1    155\n", "Name: 115, dtype: int64\n", "8    482\n", "5    471\n", "2    432\n", "3    395\n", "6    390\n", "4    389\n", "0    373\n", "1    165\n", "7    150\n", "Name: 115, dtype: int64\n", "8    473\n", "5    461\n", "6    443\n", "2    414\n", "3    391\n", "4    376\n", "0    360\n", "1    162\n", "7    154\n", "Name: 115, dtype: int64\n", "8    479\n", "5    474\n", "6    412\n", "2    410\n", "3    408\n", "4    364\n", "0    352\n", "7    159\n", "1    159\n", "Name: 115, dtype: int64\n", "8    491\n", "5    453\n", "2    420\n", "3    387\n", "6    385\n", "4    377\n", "0    361\n", "7    161\n", "1    159\n", "Name: 115, dtype: int64\n", "8    487\n", "5    452\n", "6    418\n", "3    415\n", "2    400\n", "0    384\n", "4    384\n", "1    166\n", "7    158\n", "Name: 115, dtype: int64\n", "client_labels=[[8, 0, 1, 4, 3, 5, 2, 6, 7], [8, 1, 4, 5, 2, 6, 3, 0, 7], [6, 3, 0, 7, 1, 5, 4, 8, 2], [4, 7, 5, 0, 3, 6, 8, 2, 1], [7, 8, 4, 2, 5, 6, 0, 3, 1], [7, 6, 5, 8, 2, 4, 1, 0, 3], [2, 0, 5, 8, 6, 4, 1, 3, 7], [8, 7, 1, 5, 6, 4, 0, 3, 2], [6, 3, 2, 8, 0, 5, 4, 1, 7], [0, 8, 1, 7, 4, 3, 5, 2, 6]]\n"]}], "source": ["res = []\n", "client_labels = []\n", "for idx, client in enumerate(clients_glob):\n", "    normal_data_path = os.path.join(client_data_path, f\"Client-{idx+1}\" ,\"normal\")\n", "    client_data = clients_glob[client]\n", "    client_data = [list(item[0]) + [item[1]] for item in client_data]\n", "    client_data = pd.DataFrame(client_data)\n", "    # Get the name of the last column\n", "    last_column = client_data.columns[-1]\n", "    \n", "    # Group by the last column and filter out the minority classes\n", "    client_data = client_data.groupby(last_column).filter(lambda x: len(x) >= 10)\n", "    client_labels = client_data.iloc[:,-1].unique().tolist()\n", "    # label_counts = client_labels.value_counts()\n", "    print(client_data.iloc[:,-1].value_counts())\n", "    res.append(client_labels)\n", "print(f\"client_labels={res}\")\n", "client_labels = res"]}, {"cell_type": "code", "execution_count": 193, "id": "a2b81453-96e2-4ced-bfd6-04aa63cab5f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data split:\n", " - Client 0: [196 101  43  92 122 351  26  78  99]\n", " - Client 1: [196 101  43  92 122 351  26  78  99]\n", " - Client 2: [196 101  43  92 122 351  26  78  99]\n", " - Client 3: [196 101  43  92 122 351  26  78  99]\n", " - Client 4: [196 101  43  92 122 351  26  78  99]\n", " - Client 5: [196 101  43  92 122 351  26  78  99]\n", " - Client 6: [196 101  43  92 122 351  26  78  99]\n", " - Client 7: [196 101  43  92 122 351  26  78  99]\n", " - Client 8: [196 101  43  92 122 351  26  78  99]\n", " - Client 9: [196 101  43  92 122 351  26  78  99]\n", "\n", "Client 0 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 1 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 2 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 3 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 4 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 5 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 6 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 7 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 8 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n", "Client 9 - Data shape: (1108, 115), Labels: [0 1 2 3 4 5 6 7 8]\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "def split_data_with_fixed_label_percentage(data, labels, label_distribution, label_percentage=0.1):\n", "    '''\n", "    Splits the dataset into sub-datasets based on a given label distribution.\n", "    Each label type in each sub-dataset constitutes a fixed percentage of the total data samples.\n", "\n", "    data: DataFrame or 2D array-like with data samples\n", "    labels: Series or array-like with labels\n", "    label_distribution: List of lists, each sublist contains labels for a specific client\n", "    label_percentage: Fixed percentage of each label type in each sub-dataset\n", "    '''\n", "    if not isinstance(data, pd.DataFrame):\n", "        data = pd.DataFrame(data)\n", "    if not isinstance(labels, pd.Series):\n", "        labels = pd.Series(labels)\n", "\n", "    if 'label' not in data.columns:\n", "        data['label'] = labels\n", "\n", "    # Count total samples per label\n", "    label_counts = labels.value_counts()\n", "\n", "    clients_split = []\n", "    for client_labels in label_distribution:\n", "        client_data = pd.DataFrame()\n", "\n", "        for label in client_labels:\n", "            max_samples = int(np.floor(label_counts[label] * label_percentage))\n", "            label_data = data[data['label'] == label].head(max_samples)\n", "            client_data = pd.concat([client_data, label_data])\n", "\n", "            # Update the main data to remove selected samples\n", "            data = data.drop(label_data.index)\n", "\n", "        clients_split.append((client_data.iloc[:,:-1], client_data.iloc[:,-1]))\n", "\n", "    def print_split(clients_split):\n", "        print(\"Data split:\")\n", "        for i, client in enumerate(clients_split):\n", "            split = np.sum(client[1].values.reshape(\n", "                1, -1) == np.arange(label_counts.shape[0]).reshape(-1, 1), axis=1)\n", "            print(\" - Client {}: {}\".format(i, split))\n", "        print()\n", "        # You can access the split data for each client\n", "        for i, client in enumerate(clients_split):\n", "            client_data, client_labels = client\n", "            print(\n", "                f\"Client {i} - Data shape: {client_data.shape}, Labels: {np.unique(client_labels)}\")\n", "    print_split(clients_split)\n", "\n", "    return clients_split\n", "\n", "\n", "test_normal_split = split_data_with_fixed_label_percentage(test_normal_data, \n", "                                                        test_normal_label, client_labels)\n"]}, {"cell_type": "code", "execution_count": null, "id": "68954af9-244b-432b-b59b-eb67b60ff40f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 64, "id": "7634c318-ae73-44fa-ab77-8ce5a066b29a", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "client_data_path = \"../../Data/N-BaIoT/Client_Data\"\n", "for idx, test_normal in enumerate(test_normal_split):\n", "    # client_data = np.concatenate([normal[0], abnormal[0]], axis=0)\n", "    # client_label = np.concatenate([normal[1], abnormal[1]], axis=0)\n", "    # client = np.concatenate((client_data, client_label.reshape(-1, 1)), axis=1)\n", "\n", "    test_normal_path = os.path.join(client_data_path, f\"Client-{idx+1}\" ,\"test_normal\")\n", "    if not os.path.exists(test_normal_path):\n", "        os.makedirs(test_normal_path)\n", "        \n", "    # np.savetxt(os.path.join(test_normal_path, \"data.csv\"), test_normal[0], delimiter=\",\")"]}, {"cell_type": "code", "execution_count": 183, "id": "1932b975-1e4f-4787-8e08-f5a507a47db8", "metadata": {}, "outputs": [], "source": ["from fedartml import SplitAsFederatedData\n", "\n", "# Instantiate a SplitAsFederatedData object\n", "my_federater = SplitAsFederatedData(random_state = 42)\n", "\n", "# Get federated dataset from centralized dataset\n", "clients_glob_dic, list_ids_sampled_dic, miss_class_per_node, distances = my_federater.create_clients(image_list = test_normal_data.values.tolist(), label_list = test_normal_label, \n", "                                                             num_clients = 10, prefix_cli='Local_node',\n", "                                                        method = \"dirichlet\", alpha = 1000)\n"]}, {"cell_type": "code", "execution_count": 184, "id": "a436c689-be9c-475d-9739-858f05e934fb", "metadata": {}, "outputs": [], "source": ["clients_glob = abnormal_clients_glob_dic['without_class_completion']\n", "list_ids_sampled = abnormal_list_ids_sampled_dic['without_class_completion']"]}, {"cell_type": "code", "execution_count": 185, "id": "25cd6528-8dd5-4da6-9c58-5b5045ffc87e", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3266\n", "3303\n", "3294\n", "3290\n", "3182\n", "3247\n", "3234\n", "3217\n", "3194\n", "3264\n"]}], "source": ["import os\n", "\n", "client_data_path = \"../../Data/N-BaIoT/Client_Data\"\n", "for idx, client in enumerate(clients_glob):\n", "    abnormal_data_path = os.path.join(client_data_path, f\"Client-{idx+1}\" ,\"test_normal\")\n", "    if not os.path.exists(abnormal_data_path):\n", "        os.makedirs(abnormal_data_path)\n", "    client_data = clients_glob[client]\n", "    client_data = [list(item[0]) + [item[1]] for item in client_data]\n", "    client_data = pd.DataFrame(client_data)\n", "    last_column = client_data.columns[-1]\n", "    \n", "    # Group by the last column and filter out the minority classes\n", "    client_data = client_data.groupby(last_column).filter(lambda x: len(x) >= 10)\n", "    \n", "    client_data = client_data.iloc[:, :-1]\n", "    print(len(client_data))\n", "    # client_data.to_csv(os.path.join(abnormal_data_path, \"data.csv\"), index=False, header=False)"]}, {"cell_type": "code", "execution_count": 186, "id": "4c41d903-305b-4516-ad9c-87c16b7bbd3c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8    486\n", "5    456\n", "3    432\n", "2    417\n", "6    414\n", "4    375\n", "0    367\n", "1    160\n", "7    159\n", "Name: 115, dtype: int64\n", "8    497\n", "5    472\n", "2    431\n", "3    421\n", "6    410\n", "4    376\n", "0    368\n", "1    173\n", "7    155\n", "Name: 115, dtype: int64\n", "5    471\n", "8    470\n", "2    421\n", "6    415\n", "4    414\n", "3    412\n", "0    374\n", "7    161\n", "1    156\n", "Name: 115, dtype: int64\n", "8    480\n", "5    452\n", "6    434\n", "3    422\n", "2    404\n", "4    396\n", "0    380\n", "7    164\n", "1    158\n", "Name: 115, dtype: int64\n", "8    493\n", "5    450\n", "2    402\n", "3    395\n", "6    388\n", "4    374\n", "0    367\n", "7    158\n", "1    155\n", "Name: 115, dtype: int64\n", "8    482\n", "5    471\n", "2    432\n", "3    395\n", "6    390\n", "4    389\n", "0    373\n", "1    165\n", "7    150\n", "Name: 115, dtype: int64\n", "8    473\n", "5    461\n", "6    443\n", "2    414\n", "3    391\n", "4    376\n", "0    360\n", "1    162\n", "7    154\n", "Name: 115, dtype: int64\n", "8    479\n", "5    474\n", "6    412\n", "2    410\n", "3    408\n", "4    364\n", "0    352\n", "7    159\n", "1    159\n", "Name: 115, dtype: int64\n", "8    491\n", "5    453\n", "2    420\n", "3    387\n", "6    385\n", "4    377\n", "0    361\n", "7    161\n", "1    159\n", "Name: 115, dtype: int64\n", "8    487\n", "5    452\n", "6    418\n", "3    415\n", "2    400\n", "0    384\n", "4    384\n", "1    166\n", "7    158\n", "Name: 115, dtype: int64\n", "client_labels=[[8, 0, 1, 4, 3, 5, 2, 6, 7], [8, 1, 4, 5, 2, 6, 3, 0, 7], [6, 3, 0, 7, 1, 5, 4, 8, 2], [4, 7, 5, 0, 3, 6, 8, 2, 1], [7, 8, 4, 2, 5, 6, 0, 3, 1], [7, 6, 5, 8, 2, 4, 1, 0, 3], [2, 0, 5, 8, 6, 4, 1, 3, 7], [8, 7, 1, 5, 6, 4, 0, 3, 2], [6, 3, 2, 8, 0, 5, 4, 1, 7], [0, 8, 1, 7, 4, 3, 5, 2, 6]]\n"]}], "source": ["res = []\n", "client_labels = []\n", "for idx, client in enumerate(clients_glob):\n", "    normal_data_path = os.path.join(client_data_path, f\"Client-{idx+1}\" ,\"test_normal\")\n", "    client_data = clients_glob[client]\n", "    client_data = [list(item[0]) + [item[1]] for item in client_data]\n", "    client_data = pd.DataFrame(client_data)\n", "    # Get the name of the last column\n", "    last_column = client_data.columns[-1]\n", "    \n", "    # Group by the last column and filter out the minority classes\n", "    client_data = client_data.groupby(last_column).filter(lambda x: len(x) >= 10)\n", "    client_labels = client_data.iloc[:,-1].unique().tolist()\n", "    # label_counts = client_labels.value_counts()\n", "    print(client_data.iloc[:,-1].value_counts())\n", "    res.append(client_labels)\n", "print(f\"client_labels={res}\")\n", "client_labels = res"]}, {"cell_type": "code", "execution_count": 166, "id": "87543182-1005-4087-a22c-8788d8cc4f5f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Set the font family to DejaVu Sans\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans']\n", "\n", "# Data from the table for training\n", "clients = ['Client1', 'Client2', 'Client3', 'Client4', 'Client5', 'Client6', 'Client7', 'Client8', 'Client9', 'Client10']\n", "classes = ['class0', 'class1', 'class2', 'class3', 'class4', 'class5', 'class6', 'class7', 'class8']\n", "data = np.array([\n", "    [917, 166, 56, 39, 21, 0, 0, 0, 0],   # Client1\n", "    [298, 0, 0, 0, 197, 38, 0, 220, 0],   # Client2\n", "    [0, 225, 88, 0, 0, 0, 0, 0, 0],       # Client3\n", "    [92, 285, 0, 219, 0, 0, 0, 616, 760], # Client4\n", "    [586, 0, 0, 0, 239, 1235, 0, 0, 0],   # Client5\n", "    [27, 29, 0, 182, 266, 17, 154, 275, 39], # Client6\n", "    [116, 0, 366, 986, 0, 0, 72, 57, 38], # Client7\n", "    [514, 1002, 67, 0, 0, 464, 75, 0, 0], # Client8\n", "    [708, 0, 14, 0, 348, 3213, 0, 0, 0],  # Client9\n", "    [0, 41, 0, 20, 763, 234, 0, 0, 326]   # Client10\n", "])\n", "\n", "# Plotting\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "\n", "bottom = np.zeros(len(clients))\n", "colors = plt.cm.tab20(np.linspace(0, 1, len(classes)))\n", "\n", "for i in range(len(classes)):\n", "    ax.barh(clients, data[:, i], left=bottom, label=classes[i], color=colors[i])\n", "    bottom += data[:, i]\n", "\n", "ax.set_xlabel('sample num')\n", "ax.set_ylabel('client')\n", "ax.legend(title='Classes', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "aa066ce1-be03-45df-943b-56484d77645c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Set the font family to DejaVu Sans\n", "# plt.rcParams['font.family'] = 'sans-serif'\n", "# plt.rcParams['font.sans-serif'] = ['DejaVu Sans']\n", "\n", "# Data from the table for training\n", "clients = ['Client 1', 'Client 2', 'Client 3', 'Client 4', 'Client 5', 'Client 6', 'Client 7', 'Client 8', 'Client 9', 'Client 10']\n", "devices = ['Device 0', '<PERSON>ce 1', 'Device 2', 'Device 3', 'Device 4', 'Device 5', 'Device 6', 'Device 7', 'Device 8']\n", "\n", "data = np.array([\n", "    [917, 0, 0, 0, 56, 39, 166, 0, 21],   # Client1\n", "    [298, 0, 0, 0, 197, 38, 0, 220, 0],   # Client2\n", "    [0, 225, 88, 0, 0, 0, 0, 0, 0],       # Client3\n", "    [92, 285, 0, 219, 0, 0, 0, 616, 760], # Client4\n", "    [586, 0, 0, 0, 239, 1235, 0, 0, 0],   # Client5\n", "    [27, 29, 0, 182, 266, 17, 154, 275, 39], # Client6\n", "    [116, 0, 366, 986, 0, 0, 72, 57, 38], # Client7\n", "    [514, 1002, 67, 0, 0, 464, 75, 0, 0], # Client8\n", "    [708, 0, 14, 0, 348, 3213, 0, 0, 0],  # Client9\n", "    [0, 41, 0, 20, 763, 234, 0, 0, 326]   # Client10\n", "])\n", "\n", "# Plotting\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "\n", "bottom = np.zeros(len(clients))\n", "colors = plt.cm.tab20(np.linspace(0, 1, len(devices)))\n", "\n", "for i in range(len(devices)):\n", "    ax.bar(clients, data[:, i], bottom=bottom, label=devices[i], color=colors[i])\n", "    bottom += data[:, i]\n", "\n", "plt.text(0.5, 0.95, 'Jensen-Shannon distance = 0.83', ha='center', va='center', transform=ax.transAxes, fontsize=12, fontweight='bold')\n", "\n", "ax.set_ylabel('No. of data samples', fontweight='bold', fontsize=16)\n", "ax.set_xlabel('IoT client', fontweight='bold', fontsize=16)\n", "ax.legend(title='Devices', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.yticks(np.arange(0, 4500, 500))\n", "plt.tight_layout()\n", "plt.savefig(\"non-IIDdataset.pdf\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 5, "id": "780f5ecc-5db1-476f-bbf0-7bc09a1dc960", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABIYAAAPSCAYAAAAOV9FvAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8g+/7EAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3hUZf7//9ek9xBqCBASkdB7WwXEtaEgrh8BRUUQXFBEVlwbrEpxUUQQy66NosiCIoKVpSkKgquUABKKQWJC6DWkkj6/P/LL+WaSSZhMJhySeT6uay5Ozjn3Ofd5T2Hmfe5isVqtVgEAAAAAAMDteJhdAQAAAAAAAJiDxBAAAAAAAICbIjEEAAAAAADgpkgMAQAAAAAAuCkSQwAAAAAAAG6KxBAAAAAAAICbIjEEAAAAAADgpkgMAQAAAAAAuCkSQwAAAAAAAG6KxBAAAAAAAICbqjGJoaysLK1Zs0YzZszQXXfdpebNm8tischisWjatGkOHWPFihUaNGiQIiIi5OPjo8DAQLVq1UpjxozR7t27L1n+1KlTevLJJ9WqVSv5+/urbt266tu3rxYsWCCr1XrJ8gkJCXr44YcVHR0tPz8/NWjQQP3799fKlSsdqj8AAAAAAIArWayOZDSuABs3btSf//xnu9umTp1aYXIoJydHQ4cO1TfffGOsCwoKUm5urnJzcyVJHh4emjNnjp544gm7x4iNjVX//v117tw5o3x2drby8/MlSf3799fXX38tHx8fu+VXr16toUOHKisrS5IUEhKijIwMFRYWSpJGjRqlhQsXymKxVBAFAAAAAAAA16kxLYYkKSwsTDfeeKOefvppffLJJwoPD3eo3Msvv2wkhR599FEdPXpU6enpunjxonbs2KE+ffqosLBQTz75pGJjY8uUT01N1e23365z586pdevW2r59u9LT05WZmal///vf8vb21rp16zRx4kS7509MTNTdd9+trKws9e7dW/Hx8UpNTVVqaqqmTJkiSfrwww81e/Zs5wIDAAAAAADghBrTYqigoECenp4266KionT48OFLthiKjo5WUlKS+vXrp40bN5bZnpqaqqZNmyojI0OTJk3SzJkzbba/8MILmjFjhvz9/bVv3z5FR0fbbJ85c6b+8Y9/yNPTU/v371dMTIzN9gceeEBLlixReHi4Dhw4oDp16thsf/jhhzVv3jyFhIQoKSlJYWFhlw4IAAAAAABAFdWYFkOlk0KVceLECUlS9+7d7W4PDQ01kjkZGRllti9evFiSNGzYsDJJIUmaMGGCgoKCVFBQoKVLl9psy8zMNMYQGjduXJmkkCRNnjxZkpSWlqYvv/zSsYsCAAAAAACoohqTGKqKq666SpLsdhOTiloMHTx4UFLZ5FF8fLySk5MlSbfddpvd8kFBQerbt68kaf369TbbtmzZoosXL1ZYPioqSm3atLFbHgAAAAAAoLq4RWJo3LhxkooGsB4/fryOHTsmSbJardq5c6duv/12ZWRk6JprrtHw4cNtyu7du9dYbt++fbnnKN62f//+KpXft2+fI5cEAAAAAABQZV5mV+ByGD9+vI4ePao5c+bonXfe0TvvvGMzK1l4eLgmTZqkKVOmlOmydvz4cWO5SZMm5Z6jeFtaWpoyMjIUFBRkUz4sLEz+/v6XLF/yfPbk5OQoJyfH+LuwsFDnz59XvXr1mNEMAAAAAADIarUqPT1dERER8vCouE2QWySGPDw8NHPmTLVt21aPPfaYMjIybMYSys7OVmpqqjIzM8skb9LT043lgICAcs9Rclt6erqRGCouX1HZkttLns+emTNnavr06RXuAwAAAAAAcOTIETVt2rTCfdwiMXT27FkNHTpUGzdu1M0336ypU6eqffv2unjxon7++Wc9++yzevfdd7Vu3Tr9+OOPFbYMMtvkyZP197//3fg7NTVVkZGROnLkiEJCQkysWe2Sn58vLy+3eHtckYi/+XgOzEX8zUX8zUX8zUX8zUX8zUX8zUX8XSstLU3NmjVTcHDwJfd1i6iPHDlSGzduVL9+/bRu3Tqjy1VoaKj+7//+T71791a7du30xx9/aNKkSfrPf/5jlC0ZxKysrHKTL1lZWXbLFC+X3F5R+Us9ab6+vvL19S2zPiQkhMSQC23dulW9evUyuxpui/ibj+fAXMTfXMTfXMTfXMTfXMTfXMTfXMS/ejgy5EytH3z6wIEDWr16tSTpySeftBuUhg0basSIEZKkzz//XFar1dgWERFhLBcPWm1P8baQkBCjG1nJ8ikpKcbsZBWVL3k+AAAAAACA6lTrE0MlZwlr0aJFufu1bNlSUlHLndOnTxvrS84kVnKGsdKKt7Vt29ZmfWXLt2vXrtx9cPmQoDMX8Tcfz4G5iL+5iL+5iL+5iL+5iL+5iL+5iL95an1iqOTo24cPHy53v1OnThnLJVv8xMTEKDIyUpK0du1au2UzMzO1efNmSdItt9xis61Pnz7GgNbllT98+LAOHDhgtzzM4efnZ3YV3BrxNx/PgbmIv7mIv7mIv7mIv7mIv7mIv7mIv3lqfWKoa9euxvK7775rd5/MzEwtXrxYktSxY0cFBgYa2ywWi9HNbNmyZUpKSipT/u2331ZGRoY8PT11//3322wLDAzU4MGDjfOnpqaWKT9r1ixJReML3XnnnY5fHKrNH3/8YXYV3BrxNx/PgbmIv7mIv7mIv7mIv7mIv7mIv7mIv3lqVGIoJSVFZ8+eNR6FhYWSirp/lVxfcir65s2ba9CgQZKkb775Rg888IASEhJktVqVl5en//3vf7r++uuNF+GTTz5Z5rxPPfWUwsPDlZWVpYEDByo2NlaSlJubq3fffVcvvPCCJGns2LGKiYkpU/7FF19UYGCgTpw4oUGDBun333+XVJSQevHFF/Xee+9Jkp5//nmFhYW5KlwAAAAAAAAVslhLjrR8hYuKiqqwO1ixkSNHatGiRcbfZ8+e1a233mokdCQpICBAubm5ys/PN9Y9/fTTevXVV+0eMzY2Vv3799e5c+ckFbXuyc7OVl5enqSiLmBff/213RnDJGn16tUaOnSoMftYaGioMjIyVFBQIEkaNWqUFi5c6NCI4SWlpaUpNDRUqampzErmQpmZmTYtx3B5EX/z8RyYi/ibi/ibi/ibi/ibi/ibi/ibi/i7VmVyBTWqxZCz6tevr19++UULFixQ//791ahRI+Xl5cnLy0tXXXWVhg8frs2bN5ebFJKkbt26ad++fXriiSfUsmVL5eXlKTAwUH369NH8+fO1Zs2acpNCkjRgwADt2bNHY8aMUVRUlLKzsxUWFqabb75ZK1as0AcffFDppBCqz9GjR82uglsj/ubjOTAX8TcX8TcX8TcX8TcX8TcX8TcX8TePl9kVqAx74/s4ysvLSw899JAeeughp4/RqFEjzZ07V3PnznWqfIsWLTRv3jynz4/L58KFC2ZXwa0Rf/PxHJiL+JuL+JuL+JuL+JuL+JuL+JuL+JvHLVoMAZXl4+NjdhXcGvE3H8+BuYi/uYi/uYi/uYi/uYi/uYi/uYi/eWrUGEMoizGGqofVaqVrn4mIv/l4DsxF/M1F/M1F/M1F/M1F/M1F/M1F/F2LMYaAKtq2bZvZVXBrxN98PAfmIv7mIv7mIv7mIv7mIv7mIv7mIv7mITEEAAAAAADgpkgMAXaEh4ebXQW3RvzNx3NgLuJvLuJvLuJvLuJvLuJvLuJvLuJvHhJDgB1BQUFmV8GtEX/z8RyYi/ibi/ibi/ibi/ibi/ibi/ibi/ibh8QQYMehQ4fMroJbI/7m4zkwF/E3F/E3F/E3F/E3F/E3F/E3F/E3D4khAAAAAAAAN8V09TUc09VXj7S0NOJpIuJvPp4DcxF/cxF/cxF/cxF/cxF/cxF/cxF/12K6eqCKTp06ZXYV3BrxNx/PgbmIv7mIv7mIv7mIv7mIv7mIv7mIv3lIDAF2nD9/3uwquDXibz6eA3MRf3MRf3MRf3MRf3MRf3MRf3MRf/OQGALs8PLyMrsKbo34m4/nwFzE31zE31zE31zE31zE31zE31zE3zyMMVTDMcYQAAAAAAAoiTGGgCratm2b2VVwa8TffDwH5rqS43/99dfLYrEYjwcffNDsKrnclRz/y63kc22xWLRo0SKb7YsWLSqzT1URf3MRf3MRf3MRf3MRf/PQVguwg4Z05iL+5nP2Obj++uu1adMmlxzLnVVnzH7//XfNnz9fmzZt0qFDh5SWlqaAgADVq1dP9erVU6tWrdSpUyd17txZN954ozw83O8eEq9Zc5WM/5dffqndu3cbf0dFRdXKZOSV5HK9/q1Wq5YtW6bly5drx44dOnPmjAICAhQZGalbbrlF48ePV/PmzV1yrry8PH3wwQf6/PPPFRcXp3Pnzik4OFgtWrTQgAEDNH78eNWvX7/c8gcPHtRPP/2k7du3a/v27dqzZ49yc3PLXI8r8PljLuJvLuJvHhJDgB0NGjQwuwpujfibj+fAXNUV/zfeeEPPPPOM8vLybNanpaUpLS1NiYmJ2rFjh5YuXSpJSk9PV1BQULXU5UrG699cJeP/5Zdf6qOPPjL+7tevH4mhanY5Xv+nT5/WHXfcoa1bt9qsz8nJUUpKin799Vf961//0ptvvqmxY8dW6VwHDx7UoEGDdPDgQZv1586d07lz57Rt2za98cYb+uijj3THHXfYPcbYsWPL3PSoLnz+mIv4m4v4m4fEEGBHWFiY2VVwa8TffDwH5qqO+H/xxRd64oknXH7c2ojXv+OGDBmi66+/3qXHJP7mqu74Z2Vl6frrr9eBAwcq3C87O1sPP/ywfHx8nE4Gnjx5Utddd90lp8C+cOGCBg8erDVr1uimm25y6lyuwuvfXMTfXMTfPO7XPhxwQOm7Sri8iL/5eA7MVR3xnzFjhs3fUVFRWrBggXbs2KHff/9dsbGx+vjjj/X444+rZcuWLj9/TcLr33FBQUGKioqyeVQV8TdXdcd/6tSpNkkhi8Wi6dOna//+/fruu+/UoUMHm/0ff/xxnTx50qlzTZgwwSYp5Ovrq7ffflsHDhzQ119/rWbNmhnb8vPz9dBDDyk7O7vMcfz8/NSjRw+NGzdOH3zwgR566CGn6uMIXv/mIv7mIv7mITEEALjssrOztWDBAt1xxx1q1qyZ/P39FRQUpJiYGD300EMVDj744IMP2gx0W9xa4dtvv9WgQYPUoEED+fr6qkWLFnryySd14cIFu8exWq36/PPPNWTIEF199dUKDAyUt7e3GjVqpPbt2+uuu+7SK6+8op9++qncuiQlJWny5Mnq1auX6tevLx8fH9WrV0/XXnut/vnPf+rcuXPllrU3qG92drZeffVVjRgxQkFBQQoODta1115rdO1yVlZWlnbu3GmzbtGiRXrooYfUrVs3XX311eratavuvfdevfHGG8Z4Gr6+vg6f45tvvlH//v1Vr149+fn5qW3btvrnP/9p90eWJG3dulUvv/yyhgwZok6dOqlp06by9/eXn5+fGjVqpOuuu05TpkxRcnJyueeMioqyieG0adNUUFCg999/X9dee61CQ0MVGBioLl266K233lJhYaHd45R+LlatWmU8F126dKn0c7Fr1y6NHz9enTp1UlhYmLy9vVW/fn316tVLkydP1uHDh6v9mhx19OhRPfLII4qMjJSvr6+aNm2q0aNHKyEhwaHyjgw+7cx7rXiQ85LdyCRp06ZNZc63ceNGY/t3332nadOm6Y477lD79u3VuHFj+fn5yd/fXxEREbrpppv06quv6uzZs+VeU0XvTWdeD3/88Yf+8Y9/qHfv3mrYsKF8fHxUt25dtWnTRg888IA+/vjjcsv+73//09ixY9WuXTuFhobKx8dHjRs31m233aaFCxeW6RZaWUlJSWUex48ft7u+9KOiGJYnMzNT77//vs26Bx54QFOmTFGbNm104403asWKFTavo7S0NM2fP9+pa1u5cqXNumeffVaPPvqoWrdurUGDBmnhwoU225OTk/XZZ5+VOdaaNWu0bds2vfPOOxo1apSaNm1a6foAwBXNihotNTXVKsmamppqdlVqlZSUFLOr4NaIv/mcfQ769etnlWTzKO3nn3+2Nm/evMx+pR+PPPKINTc3t0z5kSNH2uzXt29f64QJE8o9Trt27azp6ek2xygsLLQOHTr0knWQZG3VqlWZOhQWFlpnzJhh9fLyqrBsnTp1rN98843dWJXed/r06dY2bdqUe6ypU6c69ZxYrVbrsWPHyhyvvHpdSunnePjw4dbRo0eXW+8bbrjBmp+fX+Y4f/nLXxyKf2BgoHXZsmV261L6dfS3v/3N2qdPn3KPNXLkSLvHKb3f5MmTnXouLl68aB0zZswlr8nLy8s6a9asar0mR/zvf/+zhoaGlhv3tWvXlln/4Ycf2hzjww8/rPA978x7LSUlxe5nSXmPH374wThfp06dHCpTr149m3Ilueq9mZeXZ500aZLVw8OjwrqEhoaWKZuamupQ3Nq2bWuNj4939Cm/5LVW5uHMa2/FihVljmPvs6hz5842+3To0KHS55ozZ06Zc8XFxdnsU1hYaK1bt67NPoMGDbrksadOnVrh674q+A5kLuJvLuLvWpXJFZAYquFIDFWPhIQEs6vg1oi/+Zx9Di6VGNq5c6c1MDDQ4R8eo0ePLnOO0okhi8VyyeM8//zzNsew9+PkUj9WS5o8ebLD5b28vKzff/99mWOU3u9S1+Hh4eH0D8Ds7Gyrt7e3zfGCgoKsjz76qPXzzz+3HjlyxOFjlX6OSx/X3mPBggVljuNoYkiS1cfHx7p///4yxyidRHHktfDdd9+VOY4rnovCwkLrkCFDHL4mSdaXXnqp2q7pUk6fPm1t0KBBhccNCAgos66yiSFn3msJCQnVnhiSihK3Z8+erZbXg9VqtY4aNcqhepRODOXm5lbq+ps0aWI9fvx4pV8D9q61Mg9nEkPPPvtsmeP88ccfZfYbPny4zT6enp7WzMzMSp3rnnvuKfM85eXlldmvdOI1PDz8kseuzsQQ34HMRfzNRfxdqzK5ArqSAXacOXPG7Cq4NeJvvup4DqxWqx566CFlZmYa61q1aqWlS5dq79692rFjhyZPnmzTheCDDz7Q999/f8njBgYG6u2339b+/fu1dOlShYSE2OxTuqtG6dllbrnlFm3cuFEHDx5UXFyc/vvf/2rGjBn685//LC8v23kadu3apVdeecVm3X333acffvhBv/32m9atW6e+ffsa2/Lz8/XXv/71kl0+rFarunXrpm+//Vb/+c9/NH78eJvthYWF+vTTTys8Rnl8fX1166232qzLyMjQO++8o7vuukvNmjVTw4YNdeedd+q9997T+fPnHT52Xl6eGjZsqCVLlmjfvn16++235e3tbbOPva4yderU0dChQ7VgwQKtX79eu3fv1sGDB/XLL79o7ty5qlOnjrFvbm6u3nzzzUvWxWq1qkWLFvrqq68UFxen6dOnl9mnom47JY9T/Fzs3r3boedi5cqVWrFihc26du3a6csvv9SePXv00UcflZltZerUqTp06NBluabSXnnllTLv8/79+2vDhg3asWOHJk2apIsXL1b6uKU58147c+aMli1bpsTERA0ePNimfK9evZSYmGjz+NOf/mRsb9iwoUaMGKH//Oc/2rBhg/bs2aP4+Hht2bJF06ZNs+keeeHCBYe6KDnzeli1apU+/PBDm3XBwcF66aWXtGPHDh08eFDfffednnzySZvXuiS9/fbbNnHz9vbW1KlTtW3bNuMzLjIy0th+7NgxPfvss5e8jitBYmJimXUNGza85LqCgoIKu5U6cq66deuW+Ty3d66TJ0+W2wX2cuA7kLmIv7mIv4mqM0OF6keLoeqxbds2s6vg1oi/+Zx9DipqMbR582ab9d7e3tajR4+WOUbpO8WDBw+22V66xZAk63vvvWezz+zZs8vsk5GRYWwfN26czbbyuipZrVZrWlqazd8PPfSQTdmBAweWKZORkWH18/Oz2a90d4nS9QsKCjJaLxTHv127djb7DBkypNx6XsqhQ4esDRs2dKglQFBQkHXOnDl2j2PvOV67dq3NPuPHj7fZXr9+/UrXt3Q3kNatW5fZp3TrGg8PjzItiwYOHGizT/fu3cscp/T1BAQElGlJcqnn4oYbbrDZHhISYj1//rzNPj///HOZcz3zzDPVck2X0qhRI5tjtGzZskyXP3tdNCvbYsiZ91rJz5/S7/d+/fpV+lpLeuyxx2yOd+utt5bZp6L3ZrFLvR5uvvnmMp9327dvt1un0l0nWrRoYVN29uzZZcp89913Nvt4enq6rAtGdf4f3L9//zLxLSgoKLPf888/X2a/n3/+uVLnatWqlU35Zs2a2d2v9P85kqwnTpyo8NjV2WKI70DmIv7mIv6uRYshoIp69OhhdhXcGvE3X3U8B6VbDuTl5alp06ZlBnpdsmSJzX4//vhjhccNCgoqM5Vx69aty+yXkpJiLHfr1s1m21//+lfdc889evHFF7Vs2TLt2bNHBQUFkoru8ld0Hf/973/LXENQUFCZO86Xuo5hw4apXr16kv5f/EtfR8lrkIoGDi5vYNjSs/i0aNFCO3bs0D333CNPT88K65KRkaGnnnpKc+fOrXA/SWrZsqX69+9vs+5S9S62efNmjRs3Tt26dVO9evXk6+trxPCpp54qc62XcsMNN6hNmzZO1aWk++67z3guHDlOQUGBtmzZYrN9yJAhZabd/dOf/qSOHTvarLvU68JV11RSUlJSmem7R44cWeZ14YqZl5x5r1X18+e///2vHnzwQZvBv4tfV//+979t9nXkdVXyvVmsouegsLCwzPN61113qXv37naPX7LF0LFjx8oM/P3000+X+YwpPaV6QUGB/ve//13yWhxxuf8PtlqtDq2rjvNU17mqgu9A5iL+5iL+5inbnhKAduzYUe4XOFQ/4m++6ngOjh075lS5s2fPKj8/324XAKloFqfSs2f5+/uX2S8/P99YHj58uObNm2fMfpaRkaHly5fb7B8cHKw777xTL7zwgs307c5ex4kTJyrcXvKHZnH8S19HyWuQpD59+pQ7w1W/fv1sZmuSpGbNmmnZsmU6efKk1q5dqy1btuiXX37RgQMH7M5u9c9//lMTJkwo0zWspFatWpVZV7rexT/8ixUWFmr06NFlZpyqSEZGxiX3sZcQvFQM7QkMDKzUcc6dO6fc3Fyb7S1atLB77Kuuukp79uwx/j5+/HiFdXHVNZVUOikkSdHR0Q6tqyxn3mvOfv5kZWXprrvu0rp16xwuUx2vq3PnziknJ8dme+kEWXmc/XyRLv0ZY09SUlKZdXv27CmTwLQnKChI9evXr9T5QkNDy6y7ePGigoKCyqwrrXSXu8qeq7yuka44lyvxHchcxN9cxN88tBgC7Cj9IwaXF/E335X0HFit1grHeyh9J1/SJVvE+Pr66scff9Qbb7yhXr162U06paen6z//+Y969uypP/74o/IVL+VS47WUvI7i+F/qOpwVHh6uBx98UAsWLNDevXt19uxZLVy4sEwsL1y4oAMHDlR4LGfiv2DBgkolhRzlTF3sKd1KzNnjuIKrrqmky9lCwpn3mrOfPzNmzKhUUkhyLBbV8RxUB2fGhIqOji7z+Mtf/mJ3felH6VZ9jp6vtNOnT5dZVzp56enpaTOukjPnSklJsZtELX2u8PBw+fn5VepcrnQl/f/rjoi/uYi/eWgxBNhh70sgLh/ib77qeA4iIiJs/g4NDdXOnTvl4XHpexT2WnBUla+vrx5//HE9/vjjys3N1aFDh5SQkKBff/1V77zzjnH3/cKFC/rXv/6l119/3biOkl09Ro0apSlTprj0Gi73eyAsLEyjR49Wbm6uxo0bZ7Ot5GDhrvLJJ5+UOf/MmTP1pz/9ybjLv3TpUj3//PMuP7cjSrdeuJR69erJx8fHptVQ6e5AxUonGRs3blz5ClZRo0aNyqyzNyiwvXXOqOx7rfTgzo4q/bqKjIzUyy+/rI4dOxrJvlmzZum9996r2gVdQnG3yJKthmJjYx0qW/pzUpLef/993XLLLQ6d90pnr5vIvn37dNVVV9msi4uLs/m7bdu2CggIqPS5Sg4KXlhYqAMHDqhDhw7GOqvVqn379l2yjpdTTXgeazPiby7ibx5aDAF22JshA5cP8TdfdTwH119/vc3fqamp2rp1q6Kiosp9nDx5UikpKTYzlbnCmTNnbO4c+/j4qG3btho0aJCef/55PfPMMzb7l2w1U/o61q9fr8DAwHKvITw8XD/88EOZGakq4mj8k5KSZLVa7T5KdyO78847tWHDhgpbSNhLAtlLIlRV6e4yDzzwgB5++GF16tTJiNsvv/zi8vM6qvSsdpfi6empPn362Kz77LPPyoz988svv9h0I5Ok6667zrlKVkF0dHSZ19jixYvL3KlduHBhlc/lzHutZN18fHxstlfUKqb062rixIm6//771aFDB0VFRalJkybaunVrVS7HIR4eHmWe188//1y7du2yu/+FCxeM5aZNm5ZJknz55ZeKjIws9zMmICBAsbGxdlu6XWluvfXWMvUs3bXwt99+K/M+GTp0aJljlR53adGiRTbbBw8eXOb/jtLnWr9+vU38yzvX5cR3IHMRf3OFh4ebXQW3RWIIsONSXSdQvYi/+arjOejdu7c6depks2706NF66qmntGnTJv3+++/as2ePvvzyS02ePFnt2rXTNddco19//dXldfn000/VpEkT/fWvf9VHH32kn3/+WQcPHtRvv/2mzz//XO+8847N/iVbkIwbN87mx8axY8fUu3dvzZ8/X7Gxsfr999+1detWffDBBxoxYoQaN26s0aNHV6p+1RH/tWvX6qabblJUVJQef/xxLVu2zKjvtm3bNHPmTL3wwgs2ZSIjI8v8SHWF0kmyzz77TJ9//rni4+P1ww8/aMiQIVq1apXLz+uoS437Y0/pllbp6enq27evvvrqK+3du1eLFy/WHXfcYbOPl5eXxo4dW6W6Ouv++++3+fvgwYMaOHCgfvjhB8XGxmry5MllBmp2hjPvtZKv/9Kvld27d2vlypU6dOiQkpKSbAaPLr3v/PnztXbtWsXHx2vNmjXq379/uckZV5swYYLN33l5efrzn/+sV155RTt37tShQ4e0adMmPffcc2XGHyrdYmrNmjW6+eab9fnnn2vv3r367bfftHHjRr355psaMGCAmjVrpn/9619O1dNeUvmXX34pN+Fc8lE6EeOIwMBAPfzwwzbrli5dqhdffFEHDhzQhg0byiRmQkJCNGbMmEqfKyoqSoMHD7ZZN3v2bL399tuKj4/X119/rb/+9a822yMjI+0mhs6ePWszuH/pZJKkMhMAVHbsr2J8BzKXu8S/8AobdL1YZVvsXi5Xarxcia5kAIDLwmKxaOHCherXr5/RMiU7O1uvvfaaXnvttcten9OnT2vhwoUOtYq4/fbbjeVu3brpmWee0axZs4x1v//+u2k/8CsrOTlZb731lt56661L7ltdXbnuuusumxmUTpw4UeYHXOPGjZ0aTNcsgwcP1uDBg7Vy5Upj3b59+3TnnXeWW2batGm6+uqrL0Ptynr22We1ePFinTt3zli3bt06mzF6vLy8nP5xW5Kz7zVJuuaaa2z+zs3N1ZAhQ4y/mzdvbgygfNddd9kksw4cOKDbbrvNpvzlel0NGjRII0aM0OLFi411qampmjx5siZPnmyzb+lBkh977DF99dVXNjObff/99/r++++rt9KXyfTp0/Xf//7X+AFutVo1depUTZ061e7+b775ptOtCP71r39p8+bNxjhCOTk5euyxx+zu6+XlpYULF9odX+ipp5665Lhopcc0SkxMVFRUlFP1Bqqbh8WiTSdTlJpb9c/42i7Ux0v9wsMuvWMNR4shwI6SMxDh8iP+5nP2OSg9s1XpgWa7deum7777zuHZjnx9fSvVBas63H///Ro5cqTNupkzZ2rGjBnlzpRWWtOmTSt1zup4D9StW9fhfT09PTVlyhSn7tI74rHHHlO/fv3K3X7TTTeV+yPxcnCm+5zFYtGSJUvKtECwx8vLS6+88oqee+45Z6rnEo0aNdJXX31Vbrc5b29vm6TG5VD8Xiv5+r/11lvVtWtXh8pPnz5dbdu2LXf7iBEjqu01bc/ChQv11FNPOTSOWkk+Pj765ptvdM899zhcplmzZpWtXrmq+//ggIAA/fDDD+rZs2eF+/n5+em9997Tgw8+6PS5wsPD9eOPPyomJqbC/UJDQ7VixQrddNNNTp/LVfgOZC53in9qbr7O5eTxuMTDXZJntBgC7KhoBiRUv7S0tEr9iIXrOfsclOzWIdkfRPBPf/qTDhw4oE8//VRfffWVYmNjdebMGeXm5iokJETR0dHq3LmzbrjhBg0cONDu9MZVNXLkSEVHR+unn37S1q1bdfToUZ05c0ZpaWny9/dXs2bN1KNHD91///12B321WCx67rnnNGLECC1YsEDff/+94uPjdeHCBXl6eqp+/fpq1aqVevXqpf79+6tv376Vql91vAeOHDmin3/+WZs2bdL27dt16NAhHTt2TBkZGbJYLAoNDVXLli3Vp08fjRo1Sm3atHHp+Uvy9fXV+vXr9cYbb2jJkiU6ePCgfHx81KpVK40YMUKPPvqo/vOf/1Tb+S/F2f8D/Pz8NH/+fI0bN04LFy7U5s2blZycrMzMTIWEhKhFixa64YYb9Mgjj1wRLQl69+6tuLg4zZgxQ2vWrNHp06dVt25d9evXT5MnT1anTp103333VekczrzXSr7+vby8tGHDBs2YMUOrVq1SUlJSmangi9WtW1e//PKLXnnlFa1YsUJJSUkKCAhQ+/bt9fDDD2v48OGaNm1ala6nMry8vDR79mw9/PDDWrBggX788UcdPHhQaWlpCgwMVHh4uLp3716mVZNU1H1q2bJlevLJJ7Vo0SL99NNPOnz4sNLT0+Xn56fGjRurbdu26tu3rwYOHOjS9+vl+D+4UaNG+uWXX7Rs2TKjW+uZM2fk7++v5s2bq3///ho/fryaN29e5XPFxMQoLi5OH374oVauXKk9e/bo/PnzCg4OVosWLTRgwAA99thjql+/vguurOr4DmQu4g93ZbFezjlL4XJpaWkKDQ1VampqpQfLvBIUWq3ycPGgsrWdO8Rs69at6tWrl9nVcGvOPAc7duxQz549bQY3vuWWWyo9fTR4D5iN+JuL+JuL+JuL+JvLneL/dfIZncvJM7saV7x6vt66I9Lc1uvOqkyugBZDMBX9WyvHXfq4ouZIS0vT0aNHjcFqS99rKD1uDAAAAIArCy2Garia3mJIIltdGTU5Y43a6Y033tATTzxhd1unTp20bdu2MtNNAwAAwFz8BnNMTf79VZlcAYNPA7ji7Ny50+wquL2qPgfXXHON/vvf/5IUchLvAXMRf3MRf3MRf3MRf3MRf7grupIBuOLk5XH3wmyVeQ4sFouCgoLUuHFjde/eXXfffbcGDRpU6Zl48P/wHjAX8TcX8TcX8TcX8TcX8Ye7IjEE4IoTFsY4SmZz9DmYOHGiJk6cWL2VcUO8B8xF/M1F/M1F/M0VHh5udhXcGq9/uCtu5wK44kRERJhdBbfHc2Au4m8u4m8u4m8ud4h/4RU8xGrz5s3NroJdV3LMXMkdXv+APbQYAnDF2bdvn9tMFXql4jkwF/E3F/E3F/E3lzvEn1lxK8edZsV1h9c/YA+JIQAAAACSpICAALOrcFmk5uYzIxMA/P/oSgbgitOiRQuzq+D2YmJizK6CW+M9YC7iby53iP+V3C2nQ4cOZlfBris5Zqg93OHzB7CHFkMArjhZWVlmV+GyKLRa5WGxmF0Nu67UwRev5Ji5kru8B65UxN9c7jArEF2ZKsedujLBXHz+w12RGAJwxTlx4oQiIyPNrka144dB5bjTDwN3eQ9cqdwh/ldykrVx48ZmV8EuV8eMrkzAlccdPv8Be0gMAYCJ+GEAwAwkpivHnRLTAAD3Q2IIwBWne/fuZlcBMBXvAXO5S/xJTAOALXf5/AdKY/BpwI1dqQM5enp6ml2Fcl2pMUPtkpCQYHYV3BrxB4DqcyV/l7pSv4NeyTFD7UCLIcCN0ZWgcuhKULtcyWOsXKmzwrkyZsS/8q7kmAGAo/j+WTl8/8TlQGIIcHN0JYC74otp5bj6iynxrxx+GACoTfj+CVxZSAwBANwWX0zNRfwBAADMxxhDAAAAAAAAborEEAAAAAAAgJsiMQQAAAAAAOCmSAwBAAAAAAC4KRJDAAAAAAAAborEEAAAAAAAgJsiMQQAAAAAAOCmSAwBAAAAAAC4KRJDAAAAAAAAborEEAAAAAAAgJuqMYmhrKwsrVmzRjNmzNBdd92l5s2by2KxyGKxaNq0aQ4f5+TJk3rhhRfUrVs31a1bV/7+/mrevLluvfVWvfLKK8rLyyu37KlTp/Tkk0+qVatW8vf3V926ddW3b18tWLBAVqv1kudOSEjQww8/rOjoaPn5+alBgwbq37+/Vq5c6XD9AQAAAAAAXMXL7Ao4atu2bRowYECVjvHpp59q7NixSktLkyT5+fnJx8dHycnJSk5O1rp16/TII4+oTp06ZcrGxsaqf//+OnfunCQpKChI6enp2rJli7Zs2aIVK1bo66+/lo+Pj91zr169WkOHDlVWVpYkKSQkROfPn9f69eu1fv16jRo1SgsXLpTFYqnSNQIAAAAAADiqxrQYkqSwsDDdeOONevrpp/XJJ58oPDzc4bKfffaZ7rvvPqWlpWns2LHat2+fLl68qNTUVKWlpenHH3/UE088IW9v7zJlU1NTdfvtt+vcuXNq3bq1tm/frvT0dGVmZurf//63vL29tW7dOk2cONHuuRMTE3X33XcrKytLvXv3Vnx8vFJTU5WamqopU6ZIkj788EPNnj3bqbgAAAAAAAA4o8a0GOrbt6/Onz9vs27SpEkOlT1x4oQefvhhFRYW6rXXXtPf//53m+3BwcHq27ev+vbta7f8nDlzdPLkSfn7+2v16tWKjo6WJPn4+Gj8+PFKS0vTP/7xD82bN08TJ05UTEyMTfkpU6YoMzNT4eHhWrVqldEiKSgoSNOnT9fJkyc1b948vfTSSxozZozCwsIcui4AAAAAAICqqDEthjw9PZ0u+9ZbbyklJUVdunTRE088UenyixcvliQNGzbMSAqVNGHCBAUFBamgoEBLly612ZaZmWmMITRu3Di73dQmT54sSUpLS9OXX35Z6foBAAAAAAA4o8YkhqqiOLEzfPjwSo/hEx8fr+TkZEnSbbfdZnefoKAgo7XR+vXrbbZt2bJFFy9erLB8VFSU2rRpY7c8AAAAAABAdan1iaHExEQdP35cktStWzfFxcXpvvvuU+PGjeXr66umTZvqnnvu0U8//WS3/N69e43l9u3bl3ue4m379++vUvl9+/ZVeD05OTlKS0uzeQAAAAAAADijxowx5KyDBw8ayz/99JOmT5+u3Nxc+fv7y8/PT8eOHdPy5cv12Wefafr06XrhhRdsyhcnlSSpSZMm5Z6neFtaWpoyMjIUFBRkUz4sLEz+/v6XLF/yfPbMnDlT06dPL7N+x44dCgwMVNeuXXXgwAFdvHhRwcHBio6O1p49eyRJzZs3V2FhoY4cOSJJ6ty5sw4dOqSMjAwFBgYqJiZGu3btkiQ1bdpUnp6eOnz4sCSpY8eOSkpKUlpamvz8/NSuXTvFxsZKkiIiIuTn56c//vhDUlGS6+jRo7pw4YJ8fHzUuXNnbdu2TZIUHh6uoKAgHTp0SAEBAerQoUOF1wv7UlJSjNd2u3btdPz4caWkpMjb21tdu3bV1q1bJUmNGjVSSEiIfv/9d0lSmzZtdPr0aZ07d07BwcFq27ataddQkyUnJ+vEiRNl4t2wYUOFhoYa8W7durXOnj2rs2fPysPDQz169ND27dtVWFioiIgINWvWzMzLqLH27t2rzMxMNWjQQGFhYcZ7oVWrVjp//rzOnDljxHvHjh0qKChQvXr11LBhQx04cEBS0XMTGhpq5mXUWHFxccrKylJMTIxSUlJ05swZWSwW9ezZU7GxscrPz1fdunXVqFEjI95XX321MjIydPLkSUlSz549mYXTScXx79Wrl3bu3Km8vDzVrVtX4eHhxs2pFi1aKDMz04h3jx49tGfPHuXk5KhOnTqKjIys8DsJyhcXFydfX19FREQYN/NatGihrKwsnThxQpLUvXt37d27V9nZ2QoNDVVkZKTi4uIkFbUSt1gsatiwoWnXUJPFxcUpPDxc2dnZNjd+9+3bp+zsbIWEhCgqKsrmu29BQYGOHj0qSerSpYuSk5N19dVXm3YNNVlcXJxat25dpd8a8fHxfP93UkJCgpo0aaJff/1VkhQZGSlJRu+WTp06KSEhQRkZGQoICFDr1q21c+dOSUW/7by8vJSUlMRvMCedOXNGBQUFNr+PExMTlZ6eLn9/f7Vp08aId5MmTeTj46PExERJUocOHXTkyBFduHBBvr6+6tixo7Zv3y6p6PdxYGCgEhISJElt27bVyZMndf78+Sr91qhfv365M6bbY7FarVbXhOryi4qK0uHDhzV16lRNmzbN7j7Lli3TvffeK0ny8PBQZGSk5s+frxtuuEEeHh7av3+/xo8fr40bN0qSVq5cqbvuusso//LLL+u5556TJOXl5cnLy34ubf78+Ro7dqykouRO48aNJUljx47V/Pnz1aRJE+M/JXuee+45vfzyy/Lx8VFOTk65++Xk5NhsT0tLU7NmzZSamqqQkJByy13Jvk4+o3M5eWZXo0ao5+utOyIbuPSYxN9xxN9cxN9cxN9cxN9cxN9cxN9cxN9c1RF/iefAUdUV/8shLS1NoaGhDuUKan1XssLCQmPZarVq5cqVuummm+ThUXTpbdu21TfffKPw8HBJstsa50ri6+urkJAQmwcAAAAAAIAzan1iKDg42Fi+8cYb1bVr1zL7BAUFafz48ZKkPXv26NSpU3bLZ2VllXuekttKlilerqhsye0lywIAAAAAAFSnWp8YKjkuUPHMX/aUHGuluN+gVDR+TrFjx46VW754W0hIiDG+UMnyKSkpxuxkFZUveT4AAAAAAIDqVOsTQ23btpWnp+cl9ys51FLJATFLziRWcoax0oq3lR7Mt7Ll27Vrd8m6AgAAAAAAuEKtTwz5+fnpuuuukyRjdhR7imfysFgsioqKMtbHxMQYI76vXbvWbtnMzExt3rxZknTLLbfYbOvTp48x80d55Q8fPmzUrXR5AAAAAACA6lLrE0OSNGrUKEnShg0bjCnkSsrIyNA777wjSerVq5caNPh/o45bLBaNGDFCUtEMZ0lJSWXKv/3228rIyJCnp6fuv/9+m22BgYEaPHiwJOndd99VampqmfKzZs2SVDS+0J133ln5CwQAAAAAAHBCjUoMpaSk6OzZs8ajeMaxrKwsm/UZGRk25e6//3717NlTVqtVgwcP1oYNG4yyBw4c0B133KGTJ0/Kw8NDL730UpnzPvXUUwoPD1dWVpYGDhyo2NhYSVJubq7effddvfDCC5KKpqaPiYkpU/7FF19UYGCgTpw4oUGDBun333+XVNTS6MUXX9R7770nSXr++ecVFhbmomgBAAAAAABUzMvsClRGly5dbAaGLjZ79mzNnj3b+HvkyJFatGiR8beHh4e++uor3Xjjjdq/f79uuukmBQQEyNvb22jB4+3trbfffls33HBDmeOHhoZq1apV6t+/v/bv36/u3bsrODhY2dnZysvLk1TUBez111+3W+/o6GgtX75cQ4cO1ebNmxUTE6PQ0FBlZGSooKBAUlGrpqefftrp2AAAAAAAAFRWjWoxVBXh4eHauXOn5syZox49esjb21sXL15UVFSURo8erZ07d2rMmDHllu/WrZv27dunJ554Qi1btlReXp4CAwPVp08fzZ8/X2vWrJGvr2+55QcMGKA9e/ZozJgxioqKUnZ2tsLCwnTzzTdrxYoV+uCDD2wGvQYAAAAAAKhuNarFkL3xfSrD19dXTz75pJ588kmnyjdq1Ehz587V3LlznSrfokULzZs3z6myAAAAAAAAruY2LYYAAAAAAABgi8QQAAAAAACAmyIxBAAAAAAA4KZIDAEAAAAAALgpEkMAAAAAAABuisQQAAAAAACAmyIxBAAAAAAA4Ka8zK4AAAAAAAC4fEJ9SAU4wl3i5B5XCQAAAAAAVGi1ql94mNnVqDEKrVZ5WCxmV6NakRgCAAAAAMBNeFgsOnt2i/Lz08yuyhXPyytE9ev3Mbsa1Y7EEAAAAAAAbuTixSTl5Jw2uxpXPF/fhpJIDAHVzl36bboCsQIAAAAAuBK/MmEq+rdWnjv0cQUAAAAAXB4khmAq+rdWjrv0cQUAAAAAXB4khmA6+rc6zl36uAIAAAAALg8PsysAAAAAAAAAc5AYAgAAAAAAcFMkhgAAAAAAANwUYwwBgIlCffgYdhSxAlyL95TjiFXtw3PqOGIF1H68ywHAJIVWq/qFh5ldjRql0GqVh8VidjWAGo/Pn8rj86f24PVfebz+gdqNxBAAmMTDYtHZs1uUn59mdlVqBC+vENWvz6x8gCvw+VM51fH5QysMx7k6Vrz+K4f/f4Haj/+RAMBEFy8mKSfntNnVqBF8fRtK4osp4Cp8/jjO1Z8/tFipPFe3WOH17zj+/wVqPxJDgJvjjqXjiBUAwBVosVI5tFgBgOrFrxzAjXHHsvLoYw8AcAVarDiOFiu1DzfbHEescDnwKgPcGHcsK4c7lgAAAFXDjcnK48YkqhuJIcDNccfScdyxBAAAqBpuTFYONyZxOZAYAgAAAABcNtyYdBw3JnE5kBgCALgt+u07rjpiRfwdR6wAAEB14VsGAMAtMcZB5blyjAPiX3mMMQEAAKoDiSEAgFtijIPKcfUYB8S/chhjAgAAVBcSQwAAt8UYB46rjjEOiL/jGGMCAABUFw+zKwAAAAAAAABzkBgCAAAAAABwUySGAAAAAAAA3BSJIQAAAAAAADdFYggAAAAAAMBNkRgCAAAAAABwUySGAAAAAAAA3BSJIQAAAAAAADdFYggAAAAAAMBNkRgCAAAAAABwUySGAAAAAAAA3BSJIQAAAAAAADdFYggAAAAAAMBNkRgCAAAAAABwUySGAAAAAAAA3BSJIQAAAAAAADdFYggAAAAAAMBNkRgCAAAAAABwUySGAAAAAAAA3BSJIQAAAAAAADdFYggAAAAAAMBNkRgCAAAAAABwUySGAAAAAAAA3BSJIQAAAAAAADflZXYFAAAAAADA5ePtXdfsKtQI7hInEkMwnbu82VyBWAEAAACoCqu1UOHhA8yuRo1htRbKYqndna1IDMFUfChVnjt8MAEAAACoHhaLh5KTk5WTk2N2Va54vr6+ioyMNLsa1Y7EEEzFh1LluMsHEwAAAIDqc+HCBWVlZZldjSteQECAW/z+qjGJoaysLG3atEmxsbHauXOnYmNjlZycLEmaOnWqpk2bVuljPvLII3r//fclSc2bN1dSUlKF+586dUqvvvqqVq1apeTkZPn7+6tdu3YaOXKkHnroIVkslgrLJyQk6NVXX9X69et14sQJBQcHq2vXrho7dqwGDx5c6frXFnwoOc5dPpgAAAAAAJdHjUkMbdu2TQMGuK7L0Q8//KB58+Y5vH9sbKz69++vc+fOSZKCgoKUnp6uLVu2aMuWLVqxYoW+/vpr+fj42C2/evVqDR061EiAhISE6Pz581q/fr3Wr1+vUaNGaeHChZdMLgEAAAAAALhKjRqoJCwsTDfeeKOefvppffLJJwoPD3fqOFlZWRozZoy8vLzUvXv3S+6fmpqq22+/XefOnVPr1q21fft2paenKzMzU//+97/l7e2tdevWaeLEiXbLJyYm6u6771ZWVpZ69+6t+Ph4paamKjU1VVOmTJEkffjhh5o9e7ZT1wMAAAAAAOCMGpMY6tu3r86fP6/vvvtOr776qoYNGyZfX1+njvXcc88pISFBzzzzjNq1a3fJ/efMmaOTJ0/K399fq1evNpJJPj4+Gj9+vKZPny5Jmjdvng4ePFim/JQpU5SZmanw8HCtWrVKMTExkopaHU2fPl1jx46VJL300ktKSUlx6poAAAAAAAAqq8Ykhjw9PV1ynF9++UVvvfWWYmJi9PzzzztUZvHixZKkYcOGKTo6usz2CRMmKCgoSAUFBVq6dKnNtszMTK1cuVKSNG7cONWpU6dM+cmTJ0uS0tLS9OWXX1biagAAAAAAAJxXY8YYcoWcnByNHj1aVqtV8+bNk5+f3yXLxMfHG4Nc33bbbXb3CQoKUt++fbVmzRqtX7/eaEEkSVu2bNHFixcrLB8VFaU2bdrowIEDxnhDANyDt3dds6tQYxArwLV4TzmOWAEAajO3Sgy9+OKLOnDggP7617+qX79+DpXZu3evsdy+ffty92vfvr3WrFmj/fv3O13+wIED2rdvn0P1AlDzWa2FCg933aD67sBqLZTFUmMauwJXLD5/Ko/PHwBAbeU2iaFdu3bp1VdfVaNGjSo1yPPx48eN5SZNmpS7X/G2tLQ0ZWRkKCgoyKZ8WFiY/P39L1m+5PnsycnJUU5OjvF3WlraJa4AqBh3QR3n6lhZLB5KTk62eU+jfL6+voqMjDS7GkCtwOdP5fD5U/vw/cdxxAqo/dwiMZSfn6/Ro0crPz9fb731lt1xfsqTnp5uLAcEBJS7X8lt6enpRmKouHxFZUtuL3k+e2bOnGnTVa3Yjh07FBgYqK5du+rAgQO6ePGigoODFR0drT179kiSmjdvrsLCQh05ckSS1LlzZx06dEgZGRkKDAxUTEyMdu3aJUlq2rSpPD09dfjwYUlSx44dlZSUpLS0NPn5+aldu3aKjY2VJEVERMjPz09//PGHpKLWT0ePHtWFCxfk4+Ojzp07a9u2bZKk8PBwBQUF6dChQwoICFCHDh0qvF7Yl5KSYgx03q5dOx0/flwpKSny9vZW165dtXXrVklSo0aNFBISot9//12S1KZNG50+fVrnzp1TSEiIWrduxR3jSrJaC3XkyFGdOHGiTLwbNmyo0NBQI96tW7fW2bNndfbsWXl4eKhHjx7avn27CgsLFRERoWbNmunChQvKysoy85JqjICAAEVGRmrv3r3KzMxUgwYNFBYWZrwXWrVqpfPnz+vMmTNGvHfs2KGCggLVq1dPDRs21IEDByQVPTehoaFmXk6NFRcXp6ysLMXExCglJUVnzpyRxWJRz549FRsbq/z8fNWtW1eNGjUy4n311VcrIyNDJ0+elCT17NlTFovFzMuosYrj36tXL+3cuVN5eXmqW7euwsPDjVbLLVq0UGZmphHvHj16aM+ePcrJyVGdOnUUGRkpf39/Pn8qofjzJy4uTr6+voqIiDBaebdo0UJZWVk6ceKEJKl79+7au3evsrOzFRoaapSTioYPsFgsatiwoWnXUpPFxcUpPDxc2dnZxs3Ubt26ad++fcrOzlZISIiioqJsvvsWFBTo6NGjkqQuXbroyJEjuuqqaL7/VJLVWqh9+/arVatWVfqtER8fz/d/JyUkJKhJkyb69ddfJclIVhcPe9KpUyclJCQoIyNDAQEBat26tXbu3Cmp6Ledl5eXkpKS+A3mpDNnzqigoMDm93FiYqLS09Pl7++vNm3aGPFu0qSJfHx8lJiYKEnq0KGDjhw5ogsXLsjX11cdO3bU9u3bJRX9Pg4MDFRCQoIkqW3btjp58qTOnz9fpd8a9evXl4+Pj8PXZ7FarVbXhOryi4qK0uHDhzV16lRNmzat3P1mzJihF154Qbfffru++eYbm20PPvigPvroIzVv3lxJSUllyr788st67rnnJEl5eXny8rKfS5s/f74xu9jx48fVuHFjSdLYsWM1f/58NWnSxPhPyZ7nnntOL7/8snx8fCq8e2evxVCzZs2UmpqqkJCQcstdyfbs2cMXUwcFBASoY8eOLj0md4wdVx13jHn9O646Xv9HjixRTs5plx6ztvL1bahmzYa79JjE33HVEX8+fxxXHZ8/J0+uVl7eeZces7by9q7r8kQO338cVx3ff/j8d1x1fP5L/B/gqOr4/L9c0tLSFBoa6lCuoNa3GNq/f7/++c9/KigoSO+8806lywcHBxvLWVlZ5Qa05JuqZJni5Uu96Yq3lyxrj6+vr3x9fSuuNFAJ3DF2XPEdYwCuQfcExxGr2oUxnirP1WM88f3HcXz/AWq/Wp8YGj9+vHJzczV9+nSFhYUpIyPDZnt+fr4kyWq1Gtt8fX3l7e0tqaibVLFjx46Vmxg6duyYJCkkJMToRlayfEpKii5evFjuOEPF5UueDwCA2oofxpXH4Me1B2M8VQ5jPNU+JLsdR6xwOdT6xFBxv77Jkydr8uTJ5e6XnJxstNZ5/fXXNXHiREm2M4nt3btXbdq0sVu+ePaxtm3b2qwvXb5Hjx4Vlm/Xrl1Fl1MrVTQoN2wRK8C1+LLlOAZfNxc/jGsfWqw4jhYrtQs3BiqvOm4M8LvCMe4Sp1qfGKqqmJgYRUZGKjk5WWvXrtXQoUPL7JOZmanNmzdLkm655RabbX369JG/v78uXryotWvX2k0MHT582Bigs3T52s5qtaply5ZmV6NGsVqtDNoKuABfTCuPrhzm4YcxgNqCGwOVUx03BvgNVjnu8Pur1ieG7A0oXdKlBp+2WCwaMWKEZsyYoWXLlumFF15QVFSUzT5vv/22MjIy5Onpqfvvv99mW2BgoAYPHqwlS5bo3Xff1d/+9rcyM+HMmjVLUtH4QnfeeWdlL7FGs1gsurAuUQXns82uSo3gWddPdfpHm10NoFbgi2nl0GIFAOAq3BhwXHXcGOA3mOPc5fdXjUoMpaSkqKCgwPi7sLBQUtHAzWfPnjXW+/n52YzzU1VPPfWUFixYoJMnT2rgwIFavHixunXrptzcXC1cuFAvvPCCpKIZyGJiYsqUf/HFF/XFF1/oxIkTGjRokBYuXKiWLVsqMzNTr732mt577z1J0vPPP6+wsDCX1bumyIlPUd7xTLOrUSN4RwRKbvDBBFwufDF1HC1WAACoPfgN5hh3+f1VoxJDXbp00eHDh8usnz17tmbPnm38PXLkSC1atMhl5w0NDdWqVavUv39/7d+/X927d1dwcLCys7OVl5cnqagL2Ouvv263fHR0tJYvX66hQ4dq8+bNiomJUWhoqDIyMoxE16hRo/T000+7rM4AAAAAAACXwtQWDurWrZv27dunJ554Qi1btlReXp4CAwPVp08fzZ8/X2vWrKlwGvkBAwZoz549GjNmjKKiopSdna2wsDDdfPPNWrFihT744INa328RAAAAAABcWWpUi6FLjRfkjEWLFjncuqhRo0aaO3eu5s6d69S5WrRooXnz5jlVFgAAAAAAwNVoMQQAAAAAAOCmalSLIQCobfz9/c2uQo1BrAAAAADXIzEEACaxWq1q2bKl2dWoUaxWK+OxAS5CstVxxAoAUJuRGAIAk1gsFl1Yl6iC89lmV6VG8KzrpzpuMF0ocDmQmK48EtMAgNqKxBDg5rgL6rjqiFVOfIryjme6/Li1kXdEoERiCHAJEtOVQ2IaAFCbkRgC3Bh3jCuPO8YAagsS046rjsQ0N2YcVx2xIv6OI1ZA7UdiCHBj3DGuHO4YA67Fjw3HEavahRszlefKGzPEv/K4MQbUbiSGADfHHWPH0ZUJcB1+mFUeP8xqD27MVI6rb8wQ/8qpjhtjJLsdR6xwOZAYAgC4Lb5sOc7VseKHWeXQYrH24caM46rjxgzxd5yr48+NgcrjxgCqG4khAIBb4otp5bn6iyk/zBxHi0UAtQU3BiqHGwO4HEgMAQDcEl9MK4cvpgAAV+HGgOO4MYDLgcQQAMBt8cXUcXwxBQAAqJ08zK4AAAAAAAAAzEFiCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBT1ZIY2rp1qwYPHqy2bduqZ8+e+uc//6mcnJzqOBUAAAAAAACc5OVswfXr1+vOO++UJPn7+ys5OVmBgYGKjY1Vv379lJeXJ6vVKkmKjY3V5s2btX79epdUGgAAAAAAAFXndIuhX375RdnZ2crOzta1116rwMBASdJLL72k3NxcSZLFYpHFYpHVatWGDRv0xRdfuKbWAAAAAAAAqDKnE0Pbt2+XVJT8+fOf/yxJysvL09q1a41kUPGj2KefflrF6gIAAAAAAMBVnE4MJSQkGMudOnWSJO3Zs0fZ2dmSpJiYGH3xxRfq2bOnJMlqtWrXrl1VqSsAAAAAAABcyOkxhs6cOWMsN2nSRJJ04MABY93YsWP1l7/8RT4+Pho4cKAk6fjx486eDgAAAAAAAC7mdIuh1NRUY9nLqyi/dPDgQWNd+/btJUlXX321sY6ZyQAAAAAAAK4cTieG/Pz8jOXDhw9LKupKVqxFixaSpMLCQmNdWFiYs6cDAAAAAACAizmdGCruPiZJkyZN0uzZs7V27VpJkq+vr6KjoyX9vy5nFotFDRo0qEpdAQAAAAAA4EJOjzHUo0cPxcfHS5J27typnTt3ymq1ymKx6Nprr5WHR1HO6bfffjPKREZGVrG6AAAAAAAAcBWnWww9+OCDZdZZLBZJ0ogRI4x1GzduNJavueYaZ08HAAAAAAAAF3M6MXTDDTfo73//u6xWq83jL3/5i5EYys7O1n//+1+jTJ8+fapeYwAAAAAAALiE013JJGnOnDm699579eOPPyo/P1/dunXTDTfcYGzPycnRkiVLjL979+5dldMBAAAAAADAhaqUGJKkbt26qVu3bna3hYaGauDAgVU9hSQpKytLmzZtUmxsrHbu3KnY2FglJydLkqZOnapp06aVW/bYsWP66quv9MMPP2jXrl06duyYJCk8PFx/+tOfNGbMGJuEVnlOnTqlV199VatWrVJycrL8/f3Vrl07jRw5Ug899JDRla48CQkJevXVV7V+/XqdOHFCwcHB6tq1q8aOHavBgwc7HgwAAAAAAAAXqHJiqLTCwkJlZ2crICDApcfdtm2bBgwYUOlyR44cUfPmzWW1Wo11AQEBslqtSkpKUlJSkpYtW6bRo0dr3rx58vT0tHuc2NhY9e/fX+fOnZMkBQUFKT09XVu2bNGWLVu0YsUKff311/Lx8bFbfvXq1Ro6dKiysrIkSSEhITp//rzWr1+v9evXa9SoUVq4cOElk0sAAAAAAACu4vQYQyVt3LhRd999t8LDw+Xj46OQkBAlJydr9+7dWrx4sRYvXqw1a9ZU+TxhYWG68cYb9fTTT+uTTz5ReHj4JcsUFBTIarXqxhtv1EcffaRjx44pMzNTGRkZ2rdvn/7yl79Ikj744INyWx2lpqbq9ttv17lz59S6dWtt375d6enpyszM1L///W95e3tr3bp1mjhxot3yiYmJuvvuu5WVlaXevXsrPj5eqampSk1N1ZQpUyRJH374oWbPnu1UXAAAAAAAAJxRpRZD+fn5GjdunD744ANJMlrlFLd6ycnJ0YMPPiiLxSJfX1+dOnVKwcHBTp2rb9++On/+vM26SZMmXbJcWFiYYmNj1bVrV5v1Hh4eatu2rb744gsNGDBAa9eu1RtvvKHnnntOfn5+NvvOmTNHJ0+elL+/v1avXq3o6GhJko+Pj8aPH6+0tDT94x//0Lx58zRx4kTFxMTYlJ8yZYoyMzMVHh6uVatWqU6dOpKKWh1Nnz5dJ0+e1Lx58/TSSy9pzJgxCgsLq2x4AAAAAAAAKq1KLYb+9re/aeHChWUSQsV69eqltm3bymq1Kicnx2aGssoqr4vXpYSGhpZJCpVksVg0evRoSVJGRoYOHDhQZp/FixdLkoYNG2YkhUqaMGGCgoKCVFBQoKVLl9psy8zM1MqVKyVJ48aNM5JCJU2ePFmSlJaWpi+//NKh6wIAAAAAAKgqpxND27dv13vvvSeLxSKLxWIzhk9Jd9xxh7H8/fffO3u6alWyhVBBQYHNtvj4eGOQ69tuu81u+aCgIPXt21eStH79epttW7Zs0cWLFyssHxUVpTZt2tgtDwAAAAAAUF2cTgy9//77koq6j1ksFg0fPtxucqhXr17G8u7du509XbXauHGjpKKuYaW7ge3du9dYbt++fbnHKN62f//+KpXft29fhXXNyclRWlqazQMAAAAAAMAZTo8xtHnzZklFXbEmTZqkGTNmaMmSJWX2u+qqqyQVJZASExOdPV21SUxM1HvvvSdJuueeexQSEmKz/fjx48ZykyZNyj1O8ba0tDRlZGQoKCjIpnxYWJj8/f0vWb7k+eyZOXOmpk+fXmb9jh07FBgYqK5du+rAgQO6ePGigoODFR0drT179kiSmjdvrsLCQh05ckSS1LlzZx06dEgZGRkKDAxUTEyMdu3aJUlq2rSpPD09dfjwYUlSx44dlZSUpLS0NPn5+aldu3aKjY2VJEVERMjPz09//PGHpKIk19GjR3XhwgX5+Pioc+fO2rZtmyQpPDxcQUFBOnTokAICAtShQ4cKrxf2paSk6ODBg5Kkdu3a6fjx40pJSZG3t7e6du2qrVu3SpIaNWqkkJAQ/f7775KkNm3a6PTp0zp37pyCg4PVtm1b066hJktOTtaJEyfKxLthw4YKDQ014t26dWudPXtWZ8+elYeHh3r06KHt27ersLBQERERatasmZmXUWPt3btXmZmZatCggcLCwoz3QqtWrXT+/HmdOXPGiPeOHTtUUFCgevXqqWHDhkZ34datWys0NNTMy6ix4uLilJWVpZiYGKWkpOjMmTOyWCzq2bOnYmNjlZ+fr7p166pRo0ZGvK+++mplZGTo5MmTkqSePXsyC6eTiuPfq1cv7dy5U3l5eapbt67Cw8ONm1MtWrRQZmamEe8ePXpoz549ysnJUZ06dRQZGVnhdxKULy4uTr6+voqIiDBu5rVo0UJZWVk6ceKEJKl79+7au3evsrOzFRoaqsjISMXFxUkqaiVusVjUsGFD066hJouLi1N4eLiys7ON78zdunXTvn37lJ2drZCQEEVFRdl89y0oKNDRo0clSV26dFFycrKuvvpq066hJouLi1Pr1q2r9FsjPj6e7/9OSkhIUJMmTfTrr79KkiIjIyXJ6N3SqVMnJSQkKCMjQwEBAWrdurV27twpqei3nZeXl5KSkvgN5qQzZ86ooKDA5vdxYmKi0tPT5e/vrzZt2hjxbtKkiXx8fIz8R4cOHXTkyBFduHBBvr6+6tixo7Zv3y6p6PdxYGCgEhISJElt27bVyZMndf78+Sr91qhfv365M6bbY7GW1wfsEoKDg5WZmSmLxaIdO3aoS5cu8vAoaoBksViUmJioyMhIHTp0yGiF4+3trZycHGdOZ1dUVJQOHz6sqVOnljujWEUuXryovn37KjY2VvXr19evv/6qiIgIm31efvllPffcc5KkvLw8eXnZz6XNnz9fY8eOlVSU3GncuLEkaezYsZo/f76aNGli/Kdkz3PPPaeXX35ZPj4+FcYoJyfHZntaWpqaNWum1NTUMkmtmuLUWzuVdzzT7GrUCN4RgWr0t/LHzHIG8Xcc8TcX8TcX8TcX8TcX8TcX8TcX8TdXdcRf4jlwVHXF/3JIS0tTaGioQ7kCp1sMlRyLp/QsXiWVbAFTmYxVdcvPz9d9992n2NhYeXt7a+nSpWWSQlciX19f+fr6ml0NAAAAAABQCzg9xlD9+vWN5eLmg/Z88803xnKDBg2cPZ1LFRQU6P7779eXX34pLy8vffzxx7rlllvs7hscHGwsZ2VllXvMkttKlilerqhsye0lywIAAAAAAFQnpxNDXbp0kVQ0dtCLL75o9KsuVlhYqGXLluntt982Zi7r1q1b1WrrAgUFBRo+fLiWL18uT09PLVmyREOGDCl3/5KtiI4dO1bufsXbQkJCjPGFSpZPSUkxZierqHxNaLUEAAAAAABqB6cTQ4MGDZJUNJ7Qb7/9pujoaJvtXbt21f3336/s7GxjtrK//OUvVahq1RW3FFq2bJmRFLrnnnsqLFNyJrGSM4yVVryt9GC+lS3frl27CusDAAAAAADgKk4nhh544AFjJHRJys3NNZatVqsuXLhgTGVvsVgUHR19ySRMdSooKNB9992nTz/91EgKDRs27JLlYmJijOtcu3at3X0yMzONWdpKd0nr06ePMfNHeeUPHz5szNxSXpc2AAAAAAAAV3M6MeTr66tly5YpICBAkowEUOmH1WpVQECAPv74Y3l7e7us4pVR3FJo+fLl8vLy0tKlSx1KCklF1zVixAhJ0rJly5SUlFRmn7ffflsZGRny9PTU/fffb7MtMDBQgwcPliS9++67Sk1NLVN+1qxZkorGF7rzzjsrcWUAAAAAAADOczoxJEl/+tOf9OOPP6pTp06yWq12H506ddKmTZvUs2fPKlc2JSVFZ8+eNR6FhYWSigZuLrk+IyPDKFM8ptCnn35qDDRd2ZZLTz31lMLDw5WVlaWBAwcqNjZWUlErqXfffVcvvPCCpKKp6WNiYsqUf/HFFxUYGKgTJ05o0KBB+v333yUVtTR68cUX9d5770mSnn/+eYWFhVU+MAAAAAAAAE5werr6Yl26dNHOnTv1008/adOmTTaDKF9//fXq3bt3lStZ8lyHDx8us3727NmaPXu28ffIkSO1aNEiSdJPP/2kZcuWSSpq/TNhwgRNmDCh3HO8+eabZRJHoaGhWrVqlfr376/9+/ere/fuCg4OVnZ2tvLy8iQVdQF7/fXX7R4zOjpay5cv19ChQ7V582bFxMQoNDRUGRkZKigokCSNGjVKTz/9tOPBAAAAAAAAqKIqJ4aK9e7d26VJIFcpblUkSXl5eTp16lSF+5c3c1i3bt20b98+zZo1S6tWrdKRI0cUGBio9u3ba+TIkRo9erQ8PMpvgDVgwADt2bNHs2bN0rfffqsTJ04oLCxMXbp00cMPP2x0NwMAAAAAALhcXJYYuhzsje9zKddff70xK1pVNWrUSHPnztXcuXOdKt+iRQvNmzfPJXUBAAAAAACoKocSQ8nJyS47YcmZzAAAAAAAAGAehxJDUVFRslgsVT6ZxWJRfn5+lY8DAAAAAACAqnO4K5mrumMBAAAAAADgyuBwYqiqLYZILAEAAAAAAFxZaDEEAAAAAADgphxKDP3www/VXQ8AAAAAAABcZg4lhvr161fd9QAAAAAAAMBl5mF2BQAAAAAAAGAOh8cYupRdu3bpl19+0cmTJyVJ4eHh6tWrl7p27eqqUwAAAAAAAMCFqpwYWr9+vZ588knt37/f7vY2bdpozpw5uvXWW6t6KgAAAAAAALhQlbqSzZ07V7fddpv2798vq9Vq97F//34NHDhQr732mqvqDAAAAAAAABdwOjH0448/6umnnzYSQBaLpdyH1WrVM888o82bN7uy7gAAAAAAAKgCpxNDc+bMsUkIlddiqJjVatXs2bNdUmkAAAAAAABUndOJoZ9//tlICEnShAkTtHv3bqWmpio1NVW7d+/WY489JkmyWCxGGQAAAAAAAFwZnB58OjMzU1JR0mfSpEl66aWXbLZ37NhRb731lgIDAzVr1ixJUlZWVhWqCgAAAAAAAFdyusVQ8+bNjdZCQ4YMKXe/oUOHSipKIDVv3tzZ0wEAAAAAAMDFnE4M3XnnncZyWlpaufulp6cbyxUlkAAAAAAAAHB5OZ0YmjRpkiIiIiRJb7zxRrn7zZ07V5J01VVX6dlnn3X2dAAAAAAAAHAxp8cY+vXXXzVlyhRNmDBBX3/9tbp166YHH3xQ0dHRkqTExER9+OGH2r17twIDAzV16lTFxsbaPdZ1113nbDUAAAAAAADgJKcTQ9dff70x25jVatWuXbu0e/dum32KxyDKysrSyJEj7R7HYrEoPz/f2WoAAAAAAADASU4nhopZrVZj2vriRFBJJae0BwAAAAAAwJWjyomh4lZDxf9WtE9pJIwAAAAAAADMU6XEEIkdAAAAAACAmsvpxFBiYqIr6wEAAAAAAIDLzOnEUPPmzV1ZDwAAAAAAAFxmHmZXAAAAAAAAAOYgMQQAAAAAAOCmqjwr2R9//KFly5bp119/VUpKivLz88vd12KxaMOGDVU9JQAAAAAAAFygSomhl19+WdOmTVNBQcEl97VarRVOaQ8AAAAAAIDLy+nE0PLly/X888/brCPxAwAAAAAAUHM4nRj617/+Jen/JYOsVqusVqtragUAAAAAAIBq53RiaM+ePbJYLEYyqHPnzoqJiZG/v7/LKgcAAAAAAIDqU+XBpy0Wi+bMmaMnnnjCFfUBAAAAAADAZeL0dPXt27c3WgsNGDDAZRUCAAAAAADA5eF0YuiRRx4xlrdu3eqSygAAAAAAAODycTox9MADD+juu++W1WrVhAkT9O9//1upqamurBsAAAAAAACqUZXGGPr444919uxZff/993r88cf1+OOPKzQ0VHXq1LG7v8ViUUJCQlVOCQAAAAAAABepUmJo/Pjx+uGHH2xmJ7tw4YIuXLhgd//iqe0BAAAAAABgPqcTQ4sWLdL7779vJHsulfQpThwBAAAAAADgyuB0Yuidd96x+ZvEDwAAAAAAQM3idGLowIEDRhcyPz8/DRgwQNHR0QoICJCHh9NjWgMAAAAAAOAycTox5Ofnp8zMTFksFn3wwQcaNmyYK+sFAAAAAACAauZ0055rr73WWO7SpYtLKgMAAAAAAIDLx+nE0DPPPGN0GVuzZo3LKgQAAAAAAIDLw+nEUO/evfXWW29Jkp599lk99dRT2r17t9LS0lxWOQAAAAAAAFQfp8cY8vT0NJbz8vL0+uuv6/XXX6+wjMViUX5+vrOnBAAAAAAAgAs5nRiyWq3GrGTF/wIAAAAAAKDmcDoxVMxisdj8Wx4SRwAAAAAAAFeWKiWGSPYAAAAAAADUXE4nhj788ENX1gMAAAAAAACXmdOJoZEjR7qyHgAAAAAAALjMnJ6uHgAAAAAAADUbiSEAAAAAAAA3VeVZySTpxIkT2rJli44ePaqMjIwKB6WeMmWKK04JAAAAAACAKqpSYigzM1OPPvqoPv74YxUWFjpUhsQQAAAAAADAlaFKiaFBgwZp06ZNDk9bb7FYqnI6AAAAAAAAuJDTiaEvv/xSGzdulMVicSjh42jyCAAAAAAAAJeH04mhTz/91Fi2Wq0KDQ1VamqqpKKWQfXq1dPZs2dlsVgUHByssLCwqtcWAAAAAAAALuP0rGS7du0ylu+44w6dO3fOZvuOHTu0bds2NWjQQFarVe+8844SExOdrykAAAAAAABcyunE0MmTJ43lMWPGyMOj7KG6d++uqVOnKj09XXfffbfi4+OdPZ2ysrK0Zs0azZgxQ3fddZeaN29udGObNm2aQ8c4deqUnnzySbVq1Ur+/v6qW7eu+vbtqwULFjjU1S0hIUEPP/ywoqOj5efnpwYNGqh///5auXKlQ+ffuXOnhg8frqZNm8rX11eNGzfW//3f/+n77793qDwAAAAAAIArOd2VLCsry1iOioqSVNSFrDjBkpubK0nq27evsf/MmTO1aNEip863bds2DRgwwNnqKjY2Vv379zdaNgUFBSk9PV1btmzRli1btGLFCn399dfy8fGxW3716tUaOnSocd0hISE6f/681q9fr/Xr12vUqFFauHBhueMtLViwQOPGjVN+fr4kKTQ0VKdOndKXX36pL7/8UlOnTnU4wQUAAAAAAOAKTrcYCgkJMZZ9fX0lSQEBAca6o0ePSpLy8vKMdVVtGRMWFqYbb7xRTz/9tD755BOFh4c7VC41NVW33367zp07p9atW2v79u1KT09XZmam/v3vf8vb21vr1q3TxIkT7ZZPTEzU3XffraysLPXu3Vvx8fFKTU1VamqqpkyZIkn68MMPNXv2bLvlf/75Zz3yyCPKz8/XnXfeqSNHjujChQs6c+aMHn74YUnS9OnTtXz58soHBQAAAAAAwElOJ4bq1q1rLJ89e1aS1KBBA2PdO++8o9TUVL377ruSigaoPn36tLOnU9++fXX+/Hl99913evXVVzVs2DAjIXUpc+bM0cmTJ+Xv76/Vq1ere/fukiQfHx+NHz9e06dPlyTNmzdPBw8eLFN+ypQpyszMVHh4uFatWqWYmBhJRa2Opk+frrFjx0qSXnrpJaWkpJQp/8wzz6igoEAdOnTQ8uXL1bRpU0lSvXr19N5776l///6SpGeffVYFBQWVjAwAAAAAAIBznE4MNW7c2Fg+c+aMJKl169bGupUrV6pu3bo23avq1Knj7Onk6enpdNnFixdLkoYNG6bo6Ogy2ydMmKCgoCAVFBRo6dKlNtsyMzONMYTGjRtn9xomT54sSUpLS9OXX35ps+2PP/7Qli1bJElPPfWUvL29yy2flJSkH3/8sXIXBwAAAAAA4CSnE0OdOnUylnfs2CFJuuWWW4x1VqvV5mGxWNSvX78qVNU58fHxSk5OliTddtttdvcJCgoyxkJav369zbYtW7bo4sWLFZaPiopSmzZt7Jb/9ttvjeVbb73Vbvk+ffooODjYbnkAAAAAAIDq4nRiqGvXrpKKEkDffPONJGn06NGqX7++JBkzhhW3FvL29jZaxlxOe/fuNZbbt29f7n7F2/bv31+l8vv27bNbvmHDhmrYsKHdsp6enkZrq9LlAQAAAAAAqovTs5INHTpUXbp0kSQj+RMSEqJvvvlGd999t44cOWLs26BBAy1cuFCdO3euWm2dcPz4cWO5SZMm5e5XvC0tLU0ZGRkKCgqyKR8WFiZ/f/9Lli95vpJ/V3Tu4u3bt28vU760nJwc5eTkGH+npaVVuD8AAAAAAEB5nE4MBQYG2nQnK9arVy8lJCTof//7n44dO6YGDRqoT58+8vPzq1JFnZWenm4sl5w1rbSS29LT043EUHH5isqW3F7yfK4oX9rMmTONwbJL2rFjhwIDA9W1a1cdOHBAFy9eVHBwsKKjo7Vnzx5JUvPmzVVYWGgk7Tp37qxDhw4pIyNDgYGBiomJ0a5duyRJTZs2laenpw4fPixJ6tixo5KSkpSWliY/Pz+1a9dOsbGxkqSIiAj5+fnpjz/+kFTUeuro0aO6cOGCfHx81LlzZ23btk2SFB4erqCgIB06dEgBAQHq0KFDhdcL+1JSUoyB0tu1a6fjx48rJSVF3t7e6tq1q7Zu3SpJatSokUJCQvT7779Lktq0aaPTp0/r3LlzCg4OVtu2bU27hposOTlZJ06cKBPvhg0bKjQ01Ih369atdfbsWZ09e1YeHh7q0aOHtm/frsLCQkVERKhZs2ZmXkaNtXfvXmVmZqpBgwYKCwsz3gutWrXS+fPndebMGSPeO3bsUEFBgerVq6eGDRvqwIEDkoqem9DQUDMvo8aKi4tTVlaWYmJilJKSojNnzshisahnz56KjY1Vfn6+6tatq0aNGhnxvvrqq5WRkaGTJ09Kknr27GncVELlFMe/V69e2rlzp/Ly8lS3bl2Fh4cbrZ5btGihzMxMI949evTQnj17lJOTozp16igyMrLCm10oX1xcnHx9fRUREWG08m7RooWysrJ04sQJSVL37t21d+9eZWdnKzQ0VJGRkYqLi5NUNPyAxWIptxU5KhYXF6fw8HBlZ2cbN1O7deumffv2KTs7WyEhIYqKirL57ltQUGDMltylSxclJyfr6quvNu0aarK4uDi1bt26Sr814uPj+f7vpISEBDVp0kS//vqrJCkyMlKSjGFTOnXqpISEBGVkZCggIECtW7fWzp07JRX9tvPy8lJSUhK/wZx05swZFRQU2Pw+TkxMVHp6uvz9/dWmTRsj3k2aNJGPj48SExMlSR06dDBmJvf19VXHjh21fft2SUW/jwMDA5WQkCBJatu2rU6ePKnz589X6bdG/fr15ePj4/D1WaxWq9U1obr8oqKidPjwYU2dOlXTpk2zu8/LL7+s5557TpKUl5cnLy/7ubD58+cbs4sdP37cGFx77Nixmj9/vpo0aWL8p2LPc889p5dfflk+Pj42LXpuueUWffvtt+rdu7cxCLU9999/vz7++GPjA7M89loMNWvWTKmpqQoJCSm33JXs1Fs7lXc80+xq1AjeEYFq9LeuLj0m8Xcc8TcX8TcX8TcX8TcX8TcX8TcX8TdXdcRf4jlwVHXF/3JIS0tTaGioQ7kCp1sMXUpcXJwOHDigoKAgXXfddUYLnMuteFBnScrKyio3IFlZWXbLFC+X3F5R+ZJlXVG+NF9fX/n6+la4DwAAAAAAgCOcTgwdO3bMZmr38ePHKzAwULm5ubr77ruNAamlomTHkiVLdPvtt1ettk6IiIgwlo8dO1ZuYujYsWOSisZJKpnEKi6fkpKiixcvltv0urh8yfOV/Lt4e3nKKw8AAAAAAFBdnJ6VbO3atZo0aZImT56sBQsWKDAwUJL0xhtv6Ouvv7aZqj4tLU3Dhg27ZHKkOpScSazkDGOlFW8rPeZKZcu3a9fObvnTp0/rzJkzdssWFBTot99+s1seAAAAAACgujidGCoeTFiSbr31VmN54cKFkspOV3/x4kXNmzfP2dM5LSYmxhiYa+3atXb3yczM1ObNmyUVjQlUUp8+fYxWQuWVP3z4sDHAZunyN998s7FcXvmffvrJGHS6dHkAAAAAAIDq4nRiaPfu3cbytddeK6lo0Obff//dSAZ17NhRHh4ext8bNmyoQlWdY7FYNGLECEnSsmXLlJSUVGaft99+WxkZGfL09NT9999vsy0wMFCDBw+WJL377rtKTU0tU37WrFmSirrM3XnnnTbbrrrqKvXp00eS9NprrykvL69M+VdeeUVS0Uj+1113XeUuEAAAAAAAwElOJ4ZOnTplLMfExEiSMVWhJD300EPatWuXpk2bZnQpK55WzVkpKSnGdGxnz55VYWGhpKKBm0uuz8jIsCn31FNPKTw8XFlZWRo4cKAxzXpubq7effddvfDCC5KKZiArvpaSXnzxRQUGBurEiRMaNGiQcR2ZmZl68cUX9d5770mSnn/+eYWFhZUpP2vWLHl6eurXX3+16VJ3/vx5Pfroo1qzZo0k6dVXX5Wnp2eVYgQAAAAAAOAopxNDJcfLqVOnjiTZTLN+/fXXS5IGDhxorLPX2qYyunTpogYNGhiPI0eOSJJmz55ts/6xxx6zKRcaGqpVq1apXr162r9/v7p3724MMv3oo48qNzdXt9xyi15//XW7542Ojtby5csVEBCgzZs3KyYmRnXq1FFoaKimTp0qq9WqUaNG6emnn7Zb/tprr9V7770nLy8vff7552ratKnCwsJUv359vfvuu5KkqVOn6u67765SfAAAAAAAACrD6cRQQUGBsVzcQufgwYPGupYtW0qSzSxgXl5OT4JWZd26ddO+ffv0xBNPqGXLlsrLy1NgYKD69Omj+fPna82aNRVOAz9gwADt2bNHY8aMUVRUlLKzsxUWFqabb75ZK1as0AcffGB0mbPnr3/9q7Zu3ar77rtPTZo0UVZWlho2bKg777xTGzZs0LRp06rhqgEAAAAAAMrndKamTp06On36tKSiQZXbtm1rM4ZQcWKoOGlksVjUoEGDqtTV7vhAldGoUSPNnTtXc+fOdap8ixYtqjSAdteuXbV06VKnywMAAAAAALiS0y2GWrVqZSxPnjxZDRo0MMbead68udG9rGQyp1GjRs6eDgAAAAAAAC7mdGKof//+xrLValVqaqqsVqssFovNuELFAz1LRbOUAQAAAAAA4MrgdGLo0UcfVXh4uJEMKh5fx9/fX0888YSxX/GMW5LUu3fvKlQVAAAAAAAAruR0YqhOnTratGmTBg4cqKCgIPn5+alPnz5av369rrrqKknSiRMnJBUN/Ny1a1ddd911rqk1AAAAAAAAqqxK04S1bNlS33zzTbnbGzdurG3btlXlFAAAAAAAAKgmTrcYAgAAAAAAQM1GYggAAAAAAMBNkRgCAAAAAABwUySGAAAAAAAA3BSJIQAAAAAAADdFYggAAAAAAMBNkRgCAAAAAABwUySGAAAAAAAA3BSJIQAAAAAAADdFYggAAAAAAMBNkRgCAAAAAABwU1VODMXHx+vRRx9Vu3btFBoaKk9Pz3IfXl5erqgzAAAAAAAAXKBKmZrly5dr5MiRys3NldVqdVWdAAAAAAAAcBk4nRg6cuSIHnroIeXk5EiSLBZLhfuTOAIAAAAAALiyOJ0YWrBggTIzM42EkNVqlcViKZMAsrcOAAAAAAAA5nN6jKGNGzdKKkoI1alTR3PmzDESQBaLRTNmzNDAgQNltVoVERGh999/Xx988IFLKg0AAAAAAICqczoxFB8fL6koCTRlyhT9/e9/t9k+fPhwffPNN7rrrrt04sQJrVy5UiNGjKhabQEAAAAAAOAyTieGUlNTjeVrr7223P0ee+wxWa1Wffvtt3r33XedPR0AAAAAAABczOnEUMnBpuvWrStJ8vHxMdYVJ44iIiKMdR999JGzpwMAAAAAAICLOZ0YqlevnrGclZUlSQoKCjLWbd26VZK0b98+SUVjERV3PwMAAAAAAID5nJ6VrF69ejp27Jgk6fTp05KkqKgonT9/XlarVZMmTdK+ffv02WefGTOT5ebmuqbWAAAAAAAAqDKnWwy1bNnSWE5OTpYk9ezZU1JRN7Pz58/rrbfe0vHjx42p7GNiYqpYXQAAAAAAALiK04mhrl27Gsvr1q2TJN17773GuuJWQiXHIho+fLizpwMAAAAAAICLOd2V7MYbb9S2bdskSf7+/pKkvn37asSIEVq8eLGk/5cckqR+/fpp4sSJVawuAAAAAAAAXMXpxFDPnj31xRdflFn/4Ycf6tprr9WyZct07NgxNWjQQHfddZcee+wxeXk5fToAAAAAAAC4mMszNRaLRWPHjtXYsWNdfWgAAAAAAAC4kNOJoeLuYpI0ZMgQBQQEuKRCAAAAAAAAuDycTgw9+OCDxsDS119/vSIjI+3ud/jwYV111VWSiloT5efnO3tKAAAAAAAAuFCVupKVnnWsov0AAAAAAABwZXF6unpH5ebmVvcpAAAAAAAA4ASXJIYqajX0ww8/uOIUAAAAAAAAcDGHu5K9+eabevPNN+1u6927t92p6C9evKjTp0/LYrHIarUyQDUAAAAAAMAVxOHE0IULF5SUlFRmvdVq1dGjRy9Z3mKxqEWLFpWqHAAAAAAAAKpPpQefLu42VnJAaUcGoJaKprUHAAAAAADAlaHSiSF7M4w5MuvYHXfcoWeffbaypwMAAAAAAEA1cTgxdP3119v8PX36dKOl0N/+9jfVqVOnTBkfHx81aNBA11xzjdq1a1eligIAAAAAAMC1HE4M9evXT/369TP+nj59uqxWqywWi5544glFRkZWSwUBAAAAAABQPSrdlazYyJEjjeWgoCCXVAYAAAAAAACXj9OJoQ8//NCV9QAAAAAAAMBl5mF2BQAAAAAAAGAOp1sMFYuPj9ebb76pTZs26ejRo8rIyCh3X4vFovz8/KqeErWMV8MAs6tQYxArALUJn2mOq45YEX/HESsAQG1WpcTQ8uXLNXLkSOXm5jo0ZT1QmrXQqnrDWptdjRrFWmiVxcNidjUAoEr4/K88V37+E//K4/9fAEBt5XRi6MiRI3rooYeUk5MjScbU9eUhcQR7LB4WbV62WGmnT5ldlRohpGEj9R02wuxqAECV8flfOa7+/Cf+lcP/vwCA2szpxNCCBQuUmZlpJISKp64vnQCytw4oKWl3rE4nJphdjRqhYXQLvpgCqDX4/HdcdXz+E3/H8f8vAKA2c3rw6Y0bN0oqSgjVqVNHc+bMMRJAFotFM2bM0MCBA2W1WhUREaH3339fH3zwgUsqDQAAAAAAgKpzusVQfHy8pKIk0JQpU/T444/rqaeeMrYPHz5c//jHPzRkyBB98cUXWrlypdasWVP1GgMAAAA1HANaO45YAUD1cjoxlJqaaixfe+215e732GOP6fPPP9e3336rd999V48++qizpwQAAABqPAb/rjwG/wZci4SrY9wlTk4nhkoONl23bl1Jko+Pj3JzcyX9v8RRRESEsd9HH31EYggAAABujcG/K4fBvwHXIjldOe6QmHY6MVSvXj0dO3ZMkpSVlSVJCgoK0vnz5yVJW7duVYcOHbRv3z5JRWMRFXc/A3DlcJcsuCsQKwCAqzD4t+MY/BtwLZLTjnOXxLRLEkOnT5+WJEVFRen8+fOyWq2aNGmS9u3bp88++8yYmay4NRGAKwN3CyrP1XcMSDY5jlgBAAC4Bslpx7hLYtrpxFDLli21Z88eSVJycrIkqWfPntq5c6csFovOnz+vt956y2amspiYGBdUGYCrcLegclx9x4DEXOW5Q1NeAAAA4HJyOjHUtWtXrVy5UpK0bt06jRo1Svfee6/ee+89STJaCRX/KxXNVAbgysLdAse5+o4BibnKqY6mvLRCchyxAgAAqJ2cTgzdeOON2rZtmyTJ399fktS3b1+NGDFCixcvliSbpFC/fv00ceLEKlYXAGoXEnOOc3VijhZblUeLLQC1BcluxxEroPZzOjHUs2dPffHFF2XWf/jhh7r22mu1bNkyHTt2TA0aNNBdd92lxx57TF5eTp8OAACXosVW5bjL4IsAaj9uDFQeNwaA2s3lmRqLxaKxY8dq7Nixrj40AAAuRYstx7nL4IsAaj9uDFQONwaA2s/tmvB8++23mj9/vrZu3apTp07JYrGocePGuuaaazR27Fj169ev3LLp6el67bXXtHLlSiUmJsrT01MxMTEaNmyYJkyYIB8fnwrPferUKb366qtatWqVkpOT5e/vr3bt2mnkyJF66KGHZLGQhQcAAACqGzcGHMeNAaD2c5vEkNVq1bhx4/T+++8b64rHRkpMTFRiYqI+/vhjPfHEE5o7d26Z8ocPH9b111+vpKQkSVJAQIBycnK0Y8cO7dixQ0uXLtWGDRsUFhZm9/yxsbHq37+/zp07J0kKCgpSenq6tmzZoi1btmjFihX6+uuvL5lcAgAAAICajHGLHEescDk4lBi66qqrXHIyi8WihARzMvOLFi0ykkJDhgzRyy+/rJYtW0qS4uPj9eyzz+qrr77S66+/rr59++r//u//jLL5+fkaNGiQkpKS1LhxYy1evFg33XSTCgsL9dlnn2nMmDHatWuXhg8frv/+979lzp2amqrbb79d586dU+vWrfWf//xH3bt3V25urubPn68nnnhC69at08SJE/XOO+9cnoAAAAAAwGXGGE+VxxhPqG4OJYaSkpJsZhhzlpldpYpnSrv66qv1ySef2AyE3apVK3322Wdq3bq1/vjjDy1fvtwmMfTRRx8pLi5OkrRy5Updc801kiQPDw/dc889Kiws1H333afVq1drw4YNuvHGG23OPWfOHJ08eVL+/v5avXq1oqOjJUk+Pj4aP3680tLS9I9//EPz5s3TxIkTFRMTU62xAAAAAAAzMMZT5TDGEy6HSnUlq0pip6pJpao6ceKEJKlTp052Z0fz9vZW586d9ccffygjI8Nm20cffSRJ+vOf/2wkhUoaNmyYnnvuOSUmJmrx4sVlEkPFSalhw4YZSaGSJkyYoJdfflkZGRlaunSppk+f7txFAgAAAMAVjjGeHMcYT7gcPBzd0Wq1lvsob58rSXF3uF9//VX5+flltufl5Wn37t2SpO7duxvrs7Ky9NNPP0mSbrvtNrvHtlgsuvXWWyVJ69evt9kWHx+v5OTkCssHBQWpb9++dssDAAAAAABUF4daDE2dOtXu+tzcXL355pvKysqSr6+vbrvtNqNFTGJiotasWaOcnBx5e3vrb3/7m4KCglxX80oaN26c1qxZo0OHDunee+/VzJkzdfXVV0sqSt5MmjRJf/zxh1q0aKEnnnjCKHfgwAEVFhZKktq3b1/u8Yu3nTx5UufPn1fdunUlSXv37i2zT3nl16xZo/379zt/kQAAAAAAAJVQpcTQ7bffrqysLLVp00YbNmxQeHi4zfYTJ07oxhtvVHx8vH7++Wdt2rSp6jV20qBBg/T666/r2Wef1YoVK7RixQpjVrKLFy+qTp06GjdunGbMmKGQkBCj3PHjx43lJk2alHv8ktuOHz9uJIYqWz4tLU0ZGRnlJtFycnKUk5Nj/J2WllbuMQEAAAAAACri9HT1CxYs0OrVq2WxWDRr1qwySSFJaty4sWbNmqW//OUv+vnnn/Wvf/1LEydOrEp9q2TixIlq2bKlRo8erdOnT+vixYvGttzcXGVkZCg1NdVI6khSenq6sRwQUP5UgSW3lSzjbPnyEkMzZ860OwbRjh07FBgYqK5du+rAgQO6ePGigoODFR0drT179kiSmjdvrsLCQh05ckSS1LlzZx06dEgZGRkKDAxUTEyMdu3aJUlq2rSpPD09dfjwYUlSx44dlZSUpLS0NPn5+aldu3aKjY2VJEVERMjPz09//PGHpKLWT0ePHtWFCxfk4+Ojzp07a9u2bZKk8PBwBQUF6dChQwoICFCHDh3KjQnKl5KSooMHD0qS2rVrp+PHjyslJUXe3t7q2rWrtm7dKklq1KiRQkJC9Pvvv0uS2rRpo9OnT+vcuXMKDg5W27ZtTbuGmiw5OVknTpwoE++GDRsqNDTUiHfr1q119uxZnT17Vh4eHurRo4e2b9+uwsJCRUREqFmzZmZeRo21d+9eZWZmqkGDBgoLCzPeC61atdL58+d15swZI947duxQQUGB6tWrp4YNG+rAgQOSip6b0NBQMy+jxoqLi1NWVpZiYmKUkpKiM2fOyGKxqGfPnoqNjVV+fr7q1q2rRo0aGfG++uqrlZGRoZMnT0qSevbsaeqEFDVZcfx79eqlnTt3Ki8vT3Xr1lV4eLjR6rhFixbKzMw04t2jRw/t2bNHOTk5qlOnjiIjI42bY6icuLg4+fr6KiIiQvv27ZNUFO+srCxjPMvu3btr7969ys7OVmhoqCIjI41JTKKiomSxWNSwYUPTrqEmi4uLU3h4uLKzs42br926ddO+ffuUnZ2tkJAQRUVF2Xz3LSgo0NGjRyVJXbp0UXJystFrAJUTFxen1q1bV+m3Rnx8PN//nZSQkKAmTZro119/lSRFRkZKkjFsSadOnZSQkKCMjAwFBASodevW2rlzp6Si33ZeXl5KSkriN5iTzpw5o4KCApvfx4mJiUpPT5e/v7/atGljxLtJkyby8fFRYmKiJKlDhw46cuSILly4IF9fX3Xs2FHbt2+XVPT7ODAw0Ji9vW3btkYvpKr81qhfv758fHwcvr4qJYaKRUVFlbtfycGWlyxZYlpiKCsrS6NGjdLy5cvVvXt3LVmyRF26dJEk7dq1S//4xz/0n//8R2vWrNGGDRvUsWNHU+p5KZMnT9bf//534++0tDQ1a9ZM3bt3N1o6la57r169bP6OiIgwltu1a1fhviUTfm3atKlw3wYNGhjLrVq1qnDfevXqCc4LCwuziWnpmexKx7vk3yEhIXwhqqLIyEjjP2Op4niHhoaqRYsWxt89evSo/grWcqW75ZaMd506dYwx5STbMeNK7wvnlPwyGRYWZhPvbt262exbMt716tVT8+bNq7+CtVzJ+Hft2tVmW8l4169f3ybenTt3rva6uYOS8S/9eVLy/4VOnTrZbOOzxzVKxr/kzZVLxbtkq32+AzmvOP5V+a1BQsJ5xd8nS8e7cePGxvKlfts1atSommpX+xX/1i35+7j0TfbS8S55E+BSv4/r169vLAcHB1e4r6O/NSrTu8jpxNCBAweMu30//fRTuePnbN68WVLRwNTFd3XN8PTTT2v58uVq1aqVNm/eLD8/P2PbzTffrD59+qhz5846ePCgxo8fb9S75JOSlZVV7vFLbitZpnT5kt3UHClfmq+vr3x9fcvdDgAAAAAA4CiHZyUrj9Vq1VNPPaWPP/7YGKRZkgoKCrR06VI988wzRgLJrGbj6enpmjdvniRp/PjxNkmhYv7+/nrsscckSVu2bNHp06cl2Wa8jx07Vu45Sm4rWaay5UNCQkwdpBsAAAAAALgPpxNDHTt2lNVqlcViUWZmph544AGFhISodevWxtgNI0aMUGZmprGfWd2zDh48aExRX7KZVWktW7Y0lov7A7Zp00YeHkVhKjnDWGnF28LDw23GKCrZksqR8oz5AgAAAAAALhenE0MPP/ywsWyxWGS1WpWVlaWDBw/q4MGDysrKMhJC9spcTsWJHUnGYFH2nDp1ylgu7s4VEBCg3r17S5LWrl1rt5zVatW6deskSbfccovNtpiYGKPPeXnlMzMzja5rpcsDAAAAAABUF6cTQ8OHD9ewYcNktVolFSWH7D2K3X333Ro+fHjVa+yE1q1bG7NvLFiwwGg9VFJBQYHR3SwsLMxmcKiRI0dKkn744QdjRPCSPvvsM2NGrhEjRthss1gsxrply5YpKSmpTPm3335bGRkZ8vT01P333+/EFQIAAAAAAFRelcYYWrJkiaZMmSIfHx9ZrVa7Dx8fHz3//PNasmSJq+pcaf7+/vrrX/8qSdq5c6cGDRqkuLg4FRYWqrCwUHv27NGAAQP0v//9T1LRtPaenp5G+ZEjR6pDhw6yWq0aPHiwNmzYIEkqLCzUZ599pjFjxkiSbrvtNt14441lzv/UU08pPDxcWVlZGjhwoDHNe25urt5991298MILkqSxY8eWmV0KAAAAAACgujg9K5lU1EVr2rRpeuyxx7RixQpt3bpVp06dktVqVaNGjdSrVy8NHTrUZuo1s8yaNUu///671q5dazyKZ/fKyckx9rv33nv13HPP2ZT18vLS119/rT//+c9KSkrSTTfdpICAABUWFio7O1uS1KVLFy1dutTuuUNDQ7Vq1Sr1799f+/fvV/fu3RUcHKzs7Gzl5eVJKupC9vrrr1fHpQMAAAAAANhVpcRQsfr16+uRRx7RI4884orDVQt/f3+tXr1aK1eu1JIlSxQbG6vTp0/LYrGoWbNm6tmzp0aNGqWBAwfaLR8VFaU9e/Zozpw5+vzzz5WYmChvb2+1a9dO9957ryZMmCAfH59yz9+tWzft27dPs2bN0qpVq3TkyBEFBgaqffv2GjlypEaPHm0zFhIAAAAAAEB1c0liqKawWCwaMmSIhgwZ4lT54OBgTZ8+XdOnT3eqfKNGjTR37lzNnTvXqfIAAAAAAACuRBMVAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTJIYAAAAAAADcFIkhAAAAAAAAN0ViCAAAAAAAwE2RGAIAAAAAAHBTXo7sNHr0aGN5zpw5qlu3rn788Udj3Z/+9Cf5+Pi4vnYAAAAAAACoNg4lhhYtWiSLxSJJmjZtmurWravrr7/eWJeYmKjIyMjqqyUAAAAAAABczuGuZFar1aF1AAAAAAAAqBkcTgwVtw46d+5cmXUAAAAAAACoeRzqShYQEKCLFy9Kkh599FHdeuutkopaDFksFr3xxhuqU6eOQyecMmWKczUFAAAAAACASzmUGGrevLl+++03SdK2bdu0bds2Y5vVatWbb77p8AlJDAEAAAAAAFwZHOpKdsMNN9iMJ1R6bCGr1erQAwAAAAAAAFcOhxJDkyZNUr169YzkTumxhSwWyyUfAAAAAAAAuLI41JWsSZMm2rZtm1566SVt27ZNGRkZSkpKklSUFIqIiJCXl0OHAgAAAAAAwBXC4WxOdHS0FixYYPzt4eFhtAT66aefFBkZ6fraAQAAAAAAoNpUqZkP4wYBAAAAAADUXE4nhgoLC11ZDwAAAAAAAFxmLhsYKCMjQz///LOOHz8uSWrcuLGuvfZaBQUFueoUAAAAAAAAcKEqJ4ZOnz6tSZMmaenSpcrPz7c9uJeXhg8frpkzZ6phw4ZVPRUAAAAAAABcyKHp6suTkJCgnj176qOPPlJeXp6sVqvNIy8vT4sWLVKvXr2UmJjoqjoDAAAAAADABZxODBUWFuruu+9WcnKyrFarLBaL3YfVatXhw4c1dOhQBqsGAAAAAAC4gjidGFq5cqV27dplkwCy9yie0n7Xrl36/PPPXVZxAAAAAAAAVI3TiaHPPvvMWLZarbr11lv1+eefa/fu3dq9e7c+//xz3XLLLTbJoU8//bTqNQYAAAAAAIBLOD349I4dO4zlBx54QB999JHN9o4dO+rOO+/UAw88oKVLl0qStm/f7uzpAAAAAAAA4GJOtxg6ffq0sTx27Nhy93vkkUeM5TNnzjh7OgAAAAAAALiY04mhgoICY9nPz6/c/UpuK1kGAAAAAAAA5nI6MVSvXj1j+auvvip3vy+++MJuGQAAAAAAAJjL6TGGOnfurOPHj8tqtWrmzJny8PDQmDFj1KRJE0nSsWPH9P7772vWrFnG4NOdO3d2SaUBAAAAAABQdU63GBo4cKAkyWKxqKCgQP/85z8VGRkpf39/+fv7KzIyUi+99JIKCgpktVolSYMGDXJNrQEAAAAAAFBlTieGHnzwQUVEREgqSg5ZrVZZrVbl5OQoJyfH+Lu4tVBERIRGjhzpmloDAAAAAACgypxODPn7++uTTz4xBpe2WCx2H1artcy+AAAAAAAAMJ/TiSFJ6tu3rzZu3Kj27dsbLYRKPzp06KCNGzeqT58+rqozAAAAAAAAXMDpwaeL9ejRQ7/++qu2bNmiTZs26fjx45KKuo7169ePhBAAAAAAAMAVqsqJoWJ9+vQhCQQAAAAAAFCDVKkrGQAAAAAAAGouEkMAAAAAAABuisQQAAAAAACAmyIxBAAAAAAA4KZIDAH4/9i77/Coyvz94/dJJYUAkd6kSe/FsoqCDREV66qriA0EXREV+O7qqmABUUB3FRDUVVFsgKirWBDEAooE6SAiEHpvaSQkmc/vj/zmmCEJJJDkkMz7dV1cJHPKPHOfyZmZzzzPcwAAAAAAQYrCEAAAAAAAQJAqtsvVAwAAoOyIr1PP6yaUGWQFoLzhvFY4wZIThSEAAOCJYHmzVRyKOyufL1u97h9SrPss73y+bIWEhBbb/nj+Fx5ZAcWL14CiKe7z/6mIwhA8x4t94ZEVgPKCN6VFV5xvTENCQqVfFkrJScWyv3KvYpxCzjyr2HbH87/oguGDGVBaeA0ogmI+/5+qKAzBU7wxKjreGAEoD3hTWkQl8cZ0y2Zp797i3Wd5VbWqVIz58/wvoiD5YAaUKl4DCqeYz/+nKgpD8BRvjIqoBN4Y0Qup8MgKKGa8KS28IHljGlR4/hcez38AKFElWhgKDQ3s1eA4jrKyskryLlEW8cao8Ir5jRE9toqOHlsAAAAAypMSLQyZWUnuHsBJosdWEdGVHQAAAEA5U+JDyRzHkUSRCDhl0WOr8EqgKzvD0wqvJLIi/8IjKwAAgPKpxAtDFIQAIH8M5Su64hzKR/5Fx1BKAACA8qdEC0NvvPFGSe4eAMo0hvIVUTEP5SP/ImIoJQAAQLlUooWhvn37luTuAaDsYyhf4ZXEVWnIv/C4KhCAcoThsYVHVkD5x+XqAQAAAAQNhhIXHUOJgfKt2AtD/jmF/JNOAwAAAMCpgqHERcRQYqDcK5bC0Pfff6+JEyfq+++/165duyRJ1atX1/nnn697771X559/fnHcDQAAAACcPIYSFx5XZfUUWaE0nFRhKDs7W/fdd59effVVSYFXINu5c6emTZumadOmqV+/fpowYYJCQkJOrrUAAAAAgDKLoXxFx1A+lLSTKgwNGzZMkydPdn8/eviYv1D06quvKiYmRmPHjj2ZuwMAAAAAlGEM5SsihvKhFJxwYWj16tV68cUXA4pBuXsMSX8WisxML774ou666y61bNnyRO8SAAAAAFDWMZSv8LgqKErBCY/tmjx5slsIMjOFhYXpqquu0uDBgzV48GBdddVVCgsLy7PNqSApKUmjR4/WX/7yF1WrVk2RkZGqW7euunfvruHDh+vgwYP5bpecnKzhw4erTZs2io2NVaVKldSlSxeNHTtWR44cOe797tq1Sw8//LCaNWumqKgoxcfHq2vXrnrttdfyFNUAAAAAAABK2gn3GPr+++/lOI7MTI0bN9bcuXNVr17gxFibN2/WhRdeqI0bN7rbeO3bb7/VzTff7E6SHRERoejoaG3btk3btm3TvHnzdPXVV6t9+/YB223atEndunVTYmKiJCk6OloZGRlKSEhQQkKCpk6dqjlz5qhKlSr53u/ixYvVo0cP7du3T5IUGxur5ORk/fjjj/rxxx81ffp0ffrpp4qIiCixxw4AAAAAAJDbCfcY2rRpk8xMjuPoueeey1MUkqT69evrueeek5nJzNyiilfmz5+vXr16adeuXbr22mu1aNEipaen68CBA0pNTdUvv/yiRx99VJUqVQrYLisrS1deeaUSExNVq1YtzZ49W6mpqUpLS9P777+vihUrasmSJbr11lvzvd9Dhw7piiuu0L59+9S8eXMtWrRIycnJSk1N1csvv6zw8HB99dVXGjx4cCmkAAAAAAAAkOOEC0PJycnuz82aNStwvdzLUlNTT/TuTlpaWppuu+02HT58WPfff79mzJihzp07u/MgRUdHq0uXLnr66afVsGHDgG3feustrVixQpI0Y8YMXXzxxZKkkJAQ3XjjjZo0aZIkadasWZozZ06e+x4zZox27typqKgozZo1S507d5aU01vpvvvu04gRIyTlDLX7/fffSyYAAAAAAACAo5xwYSguLs79eeXKlQWu5y+oSFLFihVP9O5O2ttvv60NGzaoZs2aeu6554q07VtvvSVJ6t69u84555w8y2+66Sa3mDRlypQ8y/235V4vt/vvv1+xsbHKzs7W1KlTi9Q2AAAAAACAE3XChaFGjRpJypl4eujQoVq/fn2edf744w8NGzZMjuPIcRx3Gy/4izM33HCDKlSoUOjt0tLSNH/+fElSz549813HcRxddtllkqSvv/46YNnatWu1efPmY24fGxurrl275rs9AAAAAABASTnhwlC3bt0k5RRFtmzZoubNm+uSSy7RgAEDNGDAAF1yySVq0aKFtm3b5l5xy79NafNPEi1JnTp10ubNm9W/f3/Vq1dPERERqlGjhq688kp9/vnnebZds2aNfD6fJKl169YF3od/2c6dO7V//3739ty9qQqz/erVq4vwyAAAAAAAAE7cCV+VrH///ho3bpxb9MnOztbcuXM1d+5cdx3/5NRSznw899xzz0k298QkJia6l5PfsGGD7r//fiUnJysiIkIxMTHavXu3PvvsM3322We6++67NXnyZLfd27dvd/dTp06dAu8j97Lt27crPj7+hLZPSkpSSkqKYmNj810vIyNDGRkZ7u9JSUkF7hMAAAAAAOBYTrgw1KRJE/3zn//UM8884xZR/EUiP//tjuPon//8pxo3bnwSTT1xBw4ccH9++umnVblyZU2bNk29e/dWeHi4Nm/erCFDhmjatGl67bXX1KJFCz300EOSAifZjo6OLvA+ci/Lvc2Jbl9QYWjUqFHuZNW5JSQkKCYmRh07dtSaNWt0+PBhVaxYUQ0bNtTy5cslSaeffrp8Pp+2bNkiSWrfvr3++OMPpaSkKCYmRk2bNtWSJUskSXXr1lVoaKg2bdokSWrbtq0SExOVlJSkChUqqFWrVlq8eLEkqXbt2qpQoYI2bNggKaf309atW3Xw4EFFRESoffv2+uWXXyRJNWvWVGxsrP744w9FR0erTZs2BWaCgh04cMCdqLxVq1bavn27Dhw4oPDwcHXs2FELFy6UJNWoUUNxcXFat26dJKlFixbavXu39u3bp4oVK6ply5aePYaybPPmzdqxY0eevKtXr65KlSq5eTdv3lx79+7V3r17FRISoi5dumjRokXy+XyqXbt2vldzxPGtXLlSqampqlatmqpUqeL+LTRr1kz79+/Xnj173LwTEhKUnZ2t0047TdWrV9eaNWsk5Rybo69AicJZsWKF0tLS1LRpUx04cEB79uyR4zg688wztXjxYmVlZSk+Pl41atRw827SpIlSUlK0c+dOSdKZZ57pvkdA0fjzP+uss/Trr78qMzNT8fHxqlmzptvruHHjxkpNTXXz7tKli5YvX66MjAxVrlxZ9evXV1RUlJcPo8xasWKFIiMjVbt2ba1atUpSTt5paWnasWOHJKlz585auXKl0tPTValSJdWvX9+dc7NBgwZyHEfVq1f37DGUZStWrFDNmjWVnp7ufvnaqVMnrVq1Sunp6YqLi1ODBg0C3vtmZ2dr69atkqQOHTpo8+bNatKkiWePoSxbsWKFmjdvflKfNdauXcv7/xO0fv161alTR8uWLZOUcwVwSe60Je3atdP69euVkpKi6OhoNW/eXL/++quknM92YWFhSkxM5DPYCdqzZ4+ys7MDPh9v3LhRycnJioqKUosWLdy869Spo4iICG3cuFGS1KZNG23ZskUHDx5UZGSk2rZtq0WLFknK+XwcExPjTs3TsmVLdxTSyXzWqFq1qiIiIgr9+Bw7uppTBP75hcaNG5ezs6Pe5Pl3/dBDD2nMmDEnejcnbcGCBTr33HPd32fOnKmrr746YB2fz6eOHTtq2bJlOu2007Rz506FhYXp3Xff1S233CJJWrduXYEvJLNnz9all17q3p9/kuqRI0fq0UcflSRlZmYqLCz/Wtyrr76q/v37S8rpZVSrVq1818uvx1C9evV06NChgAnBy5QZ06S9e71uRdlQtap03Q3Fu0/yLzzy9xb5e4v8vUX+3iJ/b5G/t8jfWyWRv8QxKKySyr8UJCUlqVKlSoWqFZzwHENSTiFozJgxWrRoke688041atRIFSpUUIUKFdSoUSPdeeedWrRokadFISnwamhnnHFGnqKQlDPUbciQIZKkffv2ub1hcm+blpZW4H3kXpZ7m5Pd/miRkZGKi4sL+AcAAAAAAHAiTngoWW6dOnXSa6+9Vhy7KhG55/Zp3rx5gevlHlazadMmnXXWWapdu7Z727Zt29S2bdt8t922bZv7c+5tjt6+oEKOf/u4uLgCh5EBAAAAAAAUp5PqMVRWxMfHH3PiZ7/co+r8w+JatGihkJCcmHJfYexo/mU1a9Z0J56WAq9EVpjtmfMFAAAAAACUlqAoDEly5//xT4SZn9yXim/YsKGknEmh/fMTffnll/luZ2b66quvAu7Hr2nTpu7EYAVtn5qaqh9++CHf7QEAAAAAAEpKoYeS3XnnnSd9Z47j6PXXXz/p/ZyIO+64Q2+88Yb++OMPffzxx/lOPu2fC6lOnTrq2LGju6xv37764Ycf9O2332rhwoU666yzAradNm2ae0Wu2267LWCZ4zi67bbb9PTTT+v999/XY489pgYNGgSsM378eKWkpCg0NNSd6BoAAAAAAKCkFbow9Oabb57UpWXNzNPCUNeuXXX99ddr+vTpuvvuu5Wdna3evXsrLCxMmzdv1tChQ91LLT7zzDPu8DEppzD073//WytWrNB1112nt956SxdddJF8Pp9mzJihfv36SZJ69uypiy66KM99DxkyRK+99pp27typXr16acqUKerUqZOOHDmi119/XY899pgkqX///mratGkppAEAAAAAAHACk0+fxNXtPffmm29q9+7d+v7773X99dcrMjJS0dHROnDggLvOE088ob59+wZsFxYWpk8//VTdu3dXYmKiLr74YkVHR8vn8yk9PV2S1KFDB02dOjXf+61UqZI+++wz9ejRQ6tXr1bnzp1VsWJFpaenKzMzU1LOELIXXnihhB45AAAAAABAXkWeY8hxnOP+y2/dU0FMTIy+/fZbvfrqqzr//PMVExOjlJQU1alTRzfddJPmz5+v4cOH57ttgwYNtHz5cj3++ONq3bq1HMdReHi4OnXqpDFjxujnn39WlSpVCrzvTp06adWqVXrwwQd1xhlnKDMzUzExMTrvvPP06quv6osvvlBkZGQJPXIAAAAAAIC8Ct1jqH79+kUq8OzcuVMZGRmnTFHILyQkRHfffbfuvvvuIm9bsWJFjRgxQiNGjDih+65Ro4bGjRuncePGndD2AAAAAAAAxanQhaHExMRCrff777/r0Ucf1YwZM/IUherWrVukxgEAAAAAAKDkFNvl6nfs2KF77rlHrVu31kcffeQWhcxMlStX1nPPPafff/+9uO4OAAAAAAAAJ6nIk08f7dChQxo1apReeuklpaenu1cfMzNFR0dr0KBB+r//+z9VqlSpONoLAAAAAACAYnLChaH09HS9+OKLev7553Xw4EG3ICRJoaGhuuuuu/TEE0+oZs2axdZYAAAAAAAAFJ8iF4Z8Pp9effVVPfXUU9qxY0dAQUiSbrjhBj399NM644wzirWhAAAAAAAAKF5FKgx9+OGHeuyxx/THH3/IzNzbzUwXX3yxnn32WXXs2LHYGwkAAAAAAIDiV+jCUOfOnbVkyZKAgpDjOOrcubNGjRqlCy+8sEQaCAAAAAAAgJJR6MLQr7/+KsdxAq42VqFCBUVHR+vpp5/W008/fdx9OI6jOXPmnHhrAQAAAAAAUGxOaPJpf6+h9PR0ff/994XeJvdcRAAAAAAAAPBWyIlsdHTPIQAAAAAAAJQ9Re4xlF8hiOIQAAAAAABA2VPowtD555/PUDAAAAAAAIBypNCFoXnz5pVgMwAAAAAAAFDaTmiOIQAAAAAAAJR9FIYAAAAAAACCFIUhAAAAAACAIEVhCAAAAAAAIEhRGAIAAAAAAAhSFIYAAAAAAACCVKEvVw8AAAAAAMqBylW8bkHZECQ5URgCAAAASluQfNgoFmQFFCvz+eRcdLHXzSgzzOeTE1K+B1tRGAIAAN7gw17hlURW5F94xZwVH8qKLhg+mAGlxQkJ0S8Hvldy1iGvm3LKqxhWSWdWOd/rZpQ4CkPwHm9MC4+sAJQTfDAuuuL8YEz+RVec+fOhrGiC5YMZUJq2HN6ovUd2e92MU17ViOpBcf6hMARP8ca06Ir9GzOKTYVHVkCx4YNx0RT3B2PyL5qSKEzwoazwguWDGQB4hcIQPMUb06Ip7jemFOaKjq7sQPHhg3HhlcQHY/IvPAoTAIDyjMIQPMcb08Ir7jemFOaKhq7s5RC9wAqPrAAAAMolCkNAkKMwV3gl8o0xH7YLj8lfPUePOQAAgPKHwhAAeITCRNEx+at36DEHAABQPlEYAgCPUJgoGiZ/9RZzrAAAAJRPFIYAwEMUJgqPwgQAoNgwlLvwSiIr8i88skIpoDAEAAAAIGgwlLvoinMoN/kXHXP8oaRRGAIAAAAQNBjKXTTFPZSb/IuGOf5QGigMAQAAAAgqDOUuvJIYyk3+hcdQepQG+qMBAAAAAAAEKQpDAAAAAAAAQYrCEAAAAAAAQJCiMAQAAAAAABCkKAwBAAAAAAAEKQpDAAAAAAAAQYrCEAAAAAAAQJAK87oBAAAAAACg9FQOP83rJpQJwZIThSEAAAAAAIKEz3y6qNoVXjejzPCZTyFO+R5sRWEIAAAAKGXB8i10cSAroHiFOCHSnCelg5u9bsqpr3J9hVz0uNetKHEUhgAAgCf4sFd4JZEV+RdecWfFt/VFFwzf2AOl6o9vpB3LvG7Fqa9WO4nCEFDyeGNaeHww8BZZAcWHD8ZFV5wfjMm/6Iozf76tL6Ig+cYeALxCYQie4o1p0fHBwFt8YwkUDz4YF1ExfzAm/yIqicIE39YXXpB8Yw8AXqEwBE/xxrSI+GDgLb6xBIoXH4wLryQ+GJN/4VGYAACUYxSG4D3emBYeHwy8VQL5Mzyt8BhK6S2yAgAAKJ8oDAGARxjKV3QMpfQWQykBAADKHwpDAOARhvIVEUMpvcVQSgAAgHKJwhAAeImhfIXHUEpvMccKgHKE4bGFR1ZA+UdhCAAAAEDQYChx0RX3UGKKTYVHVigNFIYAAAAABA2GEhdRMQ8lpjBXdMzxh5JGYQgAAABAcGEoceEV81BiCnNFxBx/KAUUhgAAAAAApYfCXOExxx9KAf3RAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAglTQF4aeffZZOY7j/juW5ORkDR8+XG3atFFsbKwqVaqkLl26aOzYsTpy5Mhx72vXrl16+OGH1axZM0VFRSk+Pl5du3bVa6+9JjMrrocEAAAAAABQKEF9ufq1a9dqxIgRhVp306ZN6tatmxITEyVJ0dHRysjIUEJCghISEjR16lTNmTNHVapUyXf7xYsXq0ePHtq3b58kKTY2VsnJyfrxxx/1448/avr06fr0008VERFRLI8NAAAAAADgeIK2x5DP59Odd96p9PR0nXPOOcdcNysrS1deeaUSExNVq1YtzZ49W6mpqUpLS9P777+vihUrasmSJbr11lvz3f7QoUO64oortG/fPjVv3lyLFi1ScnKyUlNT9fLLLys8PFxfffWVBg8eXAKPFAAAAAAAIH9BWxh66aWXtGDBAt1yyy269NJLj7nuW2+9pRUrVkiSZsyYoYsvvliSFBISohtvvFGTJk2SJM2aNUtz5szJs/2YMWO0c+dORUVFadasWercubMkKSIiQvfdd5/ba2ny5Mn6/fffi+0xAgAAAAAAHEtQFoY2btyoRx99VKeddppeeOGF467/1ltvSZK6d++eb++im266SQ0bNpQkTZkyJc9y/22518vt/vvvV2xsrLKzszV16tQiPRYAAAAAAIATFZSFoX79+ik1NVXjxo1TtWrVjrluWlqa5s+fL0nq2bNnvus4jqPLLrtMkvT1118HLFu7dq02b958zO1jY2PVtWvXfLcHAAAAAAAoKUFXGHr11Vc1Z84cXXzxxbrtttuOu/6aNWvk8/kkSa1bty5wPf+ynTt3av/+/e7tK1euzLPOsbZfvXr1cdsEAAAAAABQHILqqmTbtm3T0KFDFRUV5c4LdDzbt293f65Tp06B6+Vetn37dsXHx5/Q9klJSUpJSVFsbGy+62VkZCgjI8P9PSkp6TiPAAAAAAAAIH9BVRi65557dOjQIY0ePVqNGjUq1DbJycnuz9HR0QWul3tZ7m1OdPuCCkOjRo1yJ6vOLSEhQTExMerYsaPWrFmjw4cPq2LFimrYsKGWL18uSTr99NPl8/m0ZcsWSVL79u31xx9/KCUlRTExMWratKmWLFkiSapbt65CQ0O1adMmSVLbtm2VmJiopKQkVahQQa1atdLixYslSbVr11aFChW0YcMGSTm9n7Zu3aqDBw8qIiJC7du31y+//CJJqlmzpmJjY/XHH38oOjpabdq0KTATFOzAgQPuROWtWrXS9u3bdeDAAYWHh6tjx45auHChJKlGjRqKi4vTunXrJEktWrTQ7t27tW/fPlWsWFEtW7b07DGUZZs3b9aOHTvy5F29enVVqlTJzbt58+bau3ev9u7dq5CQEHXp0kWLFi2Sz+dT7dq1Va9ePS8fRpm1cuVKpaamqlq1aqpSpYr7t9CsWTPt379fe/bscfNOSEhQdna2TjvtNFWvXl1r1qyRlHNsKlWq5OXDKLNWrFihtLQ0NW3aVAcOHNCePXvkOI7OPPNMLV68WFlZWYqPj1eNGjXcvJs0aaKUlBTt3LlTknTmmWfKcRwvH0aZ5c//rLPO0q+//qrMzEzFx8erZs2abq/jxo0bKzU11c27S5cuWr58uTIyMlS5cmXVr19fUVFRXj6MMmvFihWKjIxU7dq1tWrVKkk5eaelpWnHjh2SpM6dO2vlypVKT09XpUqVVL9+ffciJg0aNJDjOKpevbpnj6EsW7FihWrWrKn09HT3y9dOnTpp1apVSk9PV1xcnBo0aBDw3jc7O1tbt26VJHXo0EGbN29WkyZNPHsMZdmKFSvUvHnzk/qssXbtWt7/n6D169erTp06WrZsmSSpfv36kuROW9KuXTutX79eKSkpio6OVvPmzfXrr79KyvlsFxYWpsTERD6DnaA9e/YoOzs74PPxxo0blZycrKioKLVo0cLNu06dOoqIiNDGjRslSW3atNGWLVt08OBBRUZGqm3btlq0aJGknM/HMTExWr9+vSSpZcuW7iikk/msUbVqVUVERBT68TlmZsUT1antnXfeUZ8+fdS+fXstWrRIYWF/1sSGDx/uFluOjuPdd9/VLbfcIklat25dgS8ks2fPdq9utmDBAneS6pEjR+rRRx+VJGVmZgbcb26vvvqq+vfvLymnl1GtWrXyXS+/HkP16tXToUOHFBcXd+wQTlWTzpd2LPO6FWVDrXbSPd8X7z7Jv/DI31vk7y3y9xb5e4v8vUX+3iJ/b5VE/hLHoLBKKv9SkJSUpEqVKhWqVhAUPYZ27dqlwYMHKzQ0VK+++mqBxZn8VKxY0f05LS2twPVyL8u9zdHbF3RACtr+aJGRkYqMjDx2owEAAAAAAAohKCaf/sc//qF9+/apf//+at68uVJSUgL+HTlyxF336Ntq167tLtu2bVuB95F7We5tirp9XFxcgcPIAAAAAAAAilNQFIb8Y/smTpyoihUr5vk3atQod13/bcOGDZOUMydLSEhOTLmvMHY0/7KaNWu6E09LgVciK8z2zPkCAAAAAABKS1AUhk5GdHS0zj33XEnSl19+me86ZqavvvpKktx5hvyaNm3qTgxW0Papqan64Ycf8t0eAAAAAACgpARFYWjevHkyswL/PfHEE+66/ttefPFF97a+fftKkr799lt3RvDcpk2b5l6R67bbbgtY5jiOe9v777+vxMTEPNuPHz9eKSkpCg0NdSe6BgAAAAAAKGlBURg6WX379lWbNm1kZrruuus0Z84cSZLP59O0adPUr18/SVLPnj110UUX5dl+yJAhqlmzptLS0tSrVy/3Mu9HjhzRxIkT9dhjj0mS+vfvr6ZNm5bSowIAAAAAAMEuKK5KdrLCwsL06aefqnv37kpMTNTFF1+s6Oho+Xw+paenS5I6dOigqVOn5rt9pUqV9Nlnn6lHjx5avXq1OnfurIoVKyo9PV2ZmZmScoaQvfDCC6X2mAAAAAAAAOgxVEgNGjTQ8uXL9fjjj6t169ZyHEfh4eHq1KmTxowZo59//llVqlQpcPtOnTpp1apVevDBB3XGGWcoMzNTMTExOu+88/Tqq6/qiy++4DL0AAAAAACgVNFjSNLw4cM1fPjw465XsWJFjRgxQiNGjDih+6lRo4bGjRuncePGndD2AAAAAAAAxYkeQwAAAAAAAEGKwhAAAAAAAECQojAEAAAAAAAQpCgMAQAAAAAABCkKQwAAAAAAAEGKwhAAAAAAAECQojAEAAAAAAAQpCgMAQAAAAAABCkKQwAAAAAAAEGKwhAAAAAAAECQojAEAAAAAAAQpCgMAQAAAAAABCkKQwAAAAAAAEGKwhAAAAAAAECQojAEAAAAAAAQpCgMAQAAAAAABCkKQwAAAAAAAEGKwhAAAAAAAECQojAEAAAAAAAQpCgMAQAAAAAABCkKQwAAAAAAAEGKwhAAAAAAAECQojAEAAAAAAAQpCgMAQAAAAAABKkwrxsAAAAAD1Rt6nULyg6yAgCUYxSGAACAN/iwXXjFnJX5suVc91qx7rO8M1+2nJBQr5sBAMWD1+DCCZKcKAwBAIBSR2Gi6IqzMOGEhOr5r37Tlv1pxbK/8q5efLSG9mjudTMAoFjwGlw0wfDFAIUheC9IqrDFoiSyIv/CIyug2FCYKJqSKEzMW7tHq7YnFes+y6tWteMoDAEoN3gNLrxg+WKAwhA8RbW66IqzYk3+RRcM3xgApYXCROFRmCiH+LKh8MgKKHa8BhdOsLz+UhiCp6hWF01xV6zJv2iC5RsDAEDJ4ouZouOLGQAoORSG4Dmq1YVXEhVr8i+8YPnGAABQsvhipmhK5IsZeiEVHlkB5R6FIQDwEm+2Co+sAJQjfDFTeMX9xQw9toqOHltA+UZhCAA8whvTouONKQDgZNFjq2gYSg+UfxSGAMAjvDEtGoYSeIysAJQj9NgqPIbSA+UfhSEA8BBvTAuPoQTeo8cWAKBY8GVD4ZEVSgGFIQBAUKLHVtEwlAAAUBz4Yqbo+GIGJY3CEAAgaNFjq/AYSgAAKA58MVM0fDGD0kBhCAAAAABQavhipvD4YgalIcTrBgAAAAAAAMAbFIYAAAAAAACCFIUhAAAAAACAIEVhCAAAAAAAIEhRGAIAAAAAAAhSFIYAAAAAAACCFIUhAAAAAACAIEVhCAAAAAAAIEhRGAIAAAAAAAhSFIYAAAAAAACCFIUhAAAAAACAIEVhCAAAAAAAIEhRGAIAAAAAAAhSFIYAAAAAAACCFIUhAAAAAACAIEVhCAAAAAAAIEhRGAIAAAAAAAhSFIYAAAAAAACCFIUhAAAAAACAIEVhCAAAAAAAIEhRGAIAAAAAAAhSQVMY2rdvn9544w3deuutatmypWJiYhQZGam6devq6quv1syZM4+7j+TkZA0fPlxt2rRRbGysKlWqpC5dumjs2LE6cuTIcbfftWuXHn74YTVr1kxRUVGKj49X165d9dprr8nMiuNhAgAAAAAAFFqY1w0oLTVr1lRWVpb7e4UKFRQeHq5t27Zp27Zt+uSTT9SzZ09Nnz5d0dHRebbftGmTunXrpsTERElSdHS0MjIylJCQoISEBE2dOlVz5sxRlSpV8r3/xYsXq0ePHtq3b58kKTY2VsnJyfrxxx/1448/avr06fr0008VERFR/A8eAAAAAAAgH0HTYygrK0tnnnmmJkyYoPXr1+vw4cNKSUnRxo0bddddd0mSvvjiC91zzz35bnvllVcqMTFRtWrV0uzZs5Wamqq0tDS9//77qlixopYsWaJbb7013/s+dOiQrrjiCu3bt0/NmzfXokWLlJycrNTUVL388ssKDw/XV199pcGDB5dkBAAAAAAAAAGCpjA0d+5cLVy4UAMHDlSjRo3c2xs0aKDXXnvNLQi988472rJlS8C2b731llasWCFJmjFjhi6++GJJUkhIiG688UZNmjRJkjRr1izNmTMnz32PGTNGO3fuVFRUlGbNmqXOnTtLkiIiInTfffdpxIgRkqTJkyfr999/L+ZHDgAAAAAAkL+gKQx17979mMv9vYYkKSEhIWDZW2+95e7jnHPOybPtTTfdpIYNG0qSpkyZkme5/7bc6+V2//33KzY2VtnZ2Zo6depxHgkAAAAAAEDxCJrC0PFUqFDB/Tk7O9v9OS0tTfPnz5ck9ezZM99tHcfRZZddJkn6+uuvA5atXbtWmzdvPub2sbGx6tq1a77bAwAAAAAAlBQKQ//fvHnz3J/btGnj/rxmzRr5fD5JUuvWrQvc3r9s586d2r9/v3v7ypUr86xzrO1Xr15dtIYDAAAAAACcoKC5KtmxHDx4UKNGjZIkde3aVc2aNXOXbd++3f25Tp06Be4j97Lt27crPj7+hLZPSkpSSkqKYmNj810vIyNDGRkZ7u9JSUkF7hMAAAAAAOBYgr4w5PP51KdPH+3YsUMVKlTQyy+/HLA8OTnZ/Tm/y9jntyz3Nie6fUGFoVGjRrmTVeeWkJCgmJgYdezYUWvWrNHhw4dVsWJFNWzYUMuXL5cknX766fL5fO7k2u3bt9cff/yhlJQUxcTEqGnTplqyZIkkqW7dugoNDdWmTZskSW3btlViYqKSkpJUoUIFtWrVSosXL5Yk1a5dWxUqVNCGDRsk5fR+2rp1qw4ePKiIiAi1b99ev/zyiySpZs2aio2N1R9//KHo6OiA3lkovAMHDrgTlbdq1Urbt2/XgQMHFB4ero4dO2rhwoWSpBo1aiguLk7r1q2TJLVo0UK7d+/Wvn37VLFiRbVs2dKzx1CWbd68WTt27MiTd/Xq1VWpUiU37+bNm2vv3r3au3evQkJC1KVLFy1atEg+n0+1a9dWvXr1vHwYZdbKlSuVmpqqatWqqUqVKu7fQrNmzbR//37t2bPHzTshIUHZ2dk67bTTVL16da1Zs0ZSzrGpVKmSlw+jzFqxYoXS0tLUtGlTHThwQHv27JHjODrzzDO1ePFiZWVlKT4+XjVq1HDzbtKkiVJSUrRz505J0plnninHcbx8GGWWP/+zzjpLv/76qzIzMxUfH6+aNWu6vY4bN26s1NRUN+8uXbpo+fLlysjIUOXKlVW/fn1FRUV5+TDKrBUrVigyMlK1a9fWqlWrJOXknZaWph07dkiSOnfurJUrVyo9PV2VKlVS/fr13YuYNGjQQI7jqHr16p49hrJsxYoVqlmzptLT090vXzt16qRVq1YpPT1dcXFxatCgQcB73+zsbG3dulWS1KFDB23evFlNmjTx7DGUZStWrFDz5s1P6rPG2rVref9/gtavX686depo2bJlkqT69etLkjttSbt27bR+/XqlpKQoOjpazZs316+//iop57NdWFiYEhMT+Qx2gvbs2aPs7OyAz8cbN25UcnKyoqKi1KJFCzfvOnXqKCIiQhs3bpSUMyJpy5YtOnjwoCIjI9W2bVstWrRIUs7n45iYGK1fv16S1LJlS3cU0sl81qhataoiIiIK/fgcM7Piiapsuv/++91i0Ouvv64777wzYPm7776rW265RZK0bt26Al9IZs+erUsvvVSStGDBAneS6pEjR+rRRx+VJGVmZiosLP9a3Kuvvqr+/ftLyullVKtWrXzXy6/HUL169XTo0CHFxcUV6jGfanr95wet2k7Pp8JoVTtOnw/qWqz7JP/CI39vkb+3yN9b5O8t8vcW+XuL/L1VEvlLHIPCKqn8S0NSUpIqVapUqFpBUPcYGjJkiFsUeuGFF/IUhSSpYsWK7s9paWkF7iv3stzbHL19QQekoO2PFhkZqcjIyAKXAwAAAAAAFFbQTj49bNgwjR07VpI0ZswYDR48ON/1ateu7f68bdu2AveXe1nubYq6fVxcXIHDyAAAAAAAAIpTUBaGhg4dqueff16S9Nxzz+nhhx8ucN0WLVooJCQnptxXGDuaf1nNmjXdiaelwCuRFWZ75nwBAAAAAAClJegKQ0OGDNGYMWMk5RSFhg4desz1o6Ojde6550qSvvzyy3zXMTN99dVXkuTOM+TXtGlTd2KwgrZPTU3VDz/8kO/2AAAAAAAAJSWoCkNDhgwJGD52vKKQX9++fSVJ3377rTsjeG7Tpk1zr8h12223BSxzHMe97f3331diYmKe7cePH6+UlBSFhoa6E10DAAAAAACUtKApDOWeU2jcuHHHHD52tL59+6pNmzYyM1133XWaM2eOpJxL3U+bNk39+vWTJPXs2VMXXXRRnu2HDBmimjVrKi0tTb169XIv837kyBFNnDhRjz32mCSpf//+atq06Uk9TgAAAAAAgMIKiquSbd682Z1TKCQkRKNHj9bo0aMLXH/IkCEaMmSI+3tYWJg+/fRTde/eXYmJibr44osVHR0tn8+n9PR0SVKHDh00derUfPdXqVIlffbZZ+rRo4dWr16tzp07q2LFikpPT1dmZqaknCFkL7zwQnE9ZAAAAAAAgOMKisKQz+cL+HnXrl3HXD8lJSXPbQ0aNNDy5cs1ZswYffTRR9q4caPCw8PVqlUr3Xzzzbr//vsVERFR4D47deqkVatWafTo0frss8+0ZcsWxcTEqHXr1urbt6/uvPNOd5JrAAAAAACA0hAUhaEGDRrIzE56PxUrVtSIESM0YsSIE9q+Ro0aGjdunMaNG3fSbQEAAAAAADhZdFEBAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgqZcnJyRo+fLjatGmj2NhYVapUSV26dNHYsWN15MgRr5sHAAAAAACCSJjXDQgmmzZtUrdu3ZSYmChJio6OVkZGhhISEpSQkKCpU6dqzpw5qlKlircNBQAAAAAAQYEeQ6UkKytLV155pRITE1WrVi3Nnj1bqampSktL0/vvv6+KFStqyZIluvXWW71uKgAAAAAACBIUhkrJW2+9pRUrVkiSZsyYoYsvvliSFBISohtvvFGTJk2SJM2aNUtz5szxrJ0AAAAAACB4UBgqJW+99ZYkqXv37jrnnHPyLL/pppvUsGFDSdKUKVNKtW0AAAAAACA4URgqBWlpaZo/f74kqWfPnvmu4ziOLrvsMknS119/XWptAwAAAAAAwYvCUClYs2aNfD6fJKl169YFrudftnPnTu3fv79U2gYAAAAAAIIXVyUrBdu3b3d/rlOnToHr5V62fft2xcfH51knIyNDGRkZ7u+HDh2SJCUlJRVHU0tVSEiIYmNj1aR6rNdNKTP8WaWkpLjFxhNF/kVH/t4if2+Rv7fI31vk7y3y9xb5e6s485c4BkVV3PmXJn+NwMyOu65jhVkLJ+Xdd9/VLbfcIklat26dmjRpku96s2fP1qWXXipJWrBgQb5zEQ0fPlwjRowoucYCAAAAAIByYcuWLapbt+4x16HHUBnzz3/+Uw899JD7u8/n0/79+3XaaafJcRwPW1Z+JCUlqV69etqyZYvi4uK8bk7QIX/vcQy8Rf7eIn9vkb+3yN9b5O8t8vcW+Rc/M1NycrJq16593HUpDJWCihUruj+npaUVuF7uZbm3yS0yMlKRkZEBt1WuXPnkGoh8xcXFcVLyEPl7j2PgLfL3Fvl7i/y9Rf7eIn9vkb+3yL94VapUqVDrMfl0Kchdodu2bVuB6+VeVpiqHgAAAAAAwMmgMFQKWrRooZCQnKhXrlxZ4Hr+ZTVr1sx34mkAAAAAAIDiRGGoFERHR+vcc8+VJH355Zf5rmNm+uqrryTJnYAa3oiMjNQTTzyRZ8geSgf5e49j4C3y9xb5e4v8vUX+3iJ/b5G/t8jfW1yVrJS8/vrruvvuu+U4jn766SedddZZAcs//PBD3XjjjZKkb775RhdddJEXzQQAAAAAAEGEHkOlpG/fvmrTpo3MTNddd53mzJkjKeeqYtOmTVO/fv0kST179qQoBAAAAAAASgU9hkpRYmKiunfvrsTEREk5Q8x8Pp/S09MlSR06dNCcOXNUpUoVD1sJAAAAAACCBYWhUpacnKwxY8boo48+0saNGxUSEqKmTZvq5ptv1v3336+IiAivmwgAAAAAAIIEhSEAAAAAAIAgxRxDAAAAAAAAQYrCEAAAQDmTmZkpSaJjuDdSU1PJ3kMfffSR3n33Xa+bEdR8Pp/XTQBOGWXh9YDCEFAOlYWTD1ASjhw5Iom/Aa+sWLFCgwYNUmpqqtdNCVqrVq3S7bffrk8//VSS5DiOxy0KLhs3btQzzzyjhx56SPPmzePDcSlbvXq1Lr30Ul1//fX697//rcOHD3vdpKBy4MABHTx4UIcOHVJICB8zAb/cr8Wn6utCmNcNAFA8UlJS9Pnnn+uaa65RRESEzIwPBKXo8OHDmjZtmvbu3avdu3erY8eOateunZo1a+Z104JCWlqa/vvf/2rFihXas2ePatSooYsuukjXX3+9100LCtnZ2br//vv1yiuvSJJatmypAQMGeNyq4JKSkqJHHnlEL7/8siSpdu3a6tmzp6Kjoz1uWXBIS0vTY489pldeecUtRkRFRalVq1aqXr26x60r/45+/ktS5cqVlZaWpgoVKvB+qISlpqbqxRdf1DfffKO0tDTt2rVL1157ra699lqdd955vCctYWlpaZo6dap27dqlw4cP6+yzz1b79u1Vr149r5sW9Hbu3Km3335bSUlJSkpKUq9evdSmTRvVqlXL66blZQDKvIkTJ1pYWJhVqVLFpk+fbmZmPp/P41YFj1deecXq1q1rjuNYSEiIOY5jERERVr16dZs1a5YdPnzY6yaWa6+88opVr149IH//v3/84x+2Y8cOM+NvoiT99ttvFhMTY6GhoeY4jtWrV8+2bt3qdbOCxvjx461ixYrmOI5VrlzZhg8fbunp6V43K2jMnTvX2rZta47jWGhoqN133302e/ZsW7RoEeedUpD7+V+pUiW78sorzXEci42NtT179njdvHLvtddes/j4ePd1NyIiwv25devWtnr1ajPjNbikTJ482WrVqhXw3icqKsoaNWpks2bNsuTkZDMzy87O9rilwWf06NEWExMTcGyio6OtXbt2NnfuXMvIyDCzU+dvg8IQUIYlJibaPffcE/BC8Ne//tV2795tZrwIlLTly5dbjx493Py7d+9ud911l51//vlWv359cxzHOnfubB9//LHXTS2Xli5dapdffrmb/2WXXWYTJkywoUOH2nnnnWeO41iDBg1s4sSJXje13Fu3bp05jmMtW7a02rVrm+M4NmzYMK+bVe7Nnj3bWrdu7f4N3HHHHbZx40Z3Oa8BJW/Pnj124YUXmuM41rt3b9uwYYPXTQoas2fPtlatWgU8/xMTE+2nn36ymjVrWkxMjM2cOdPrZpZbu3fvtrvuusvN/69//av9+OOP9vXXX9tDDz3kFisGDBjgdVPLpb1799qdd97p5n/11Vfb5MmT7e6773ZfFxo0aGD//Oc/vW5qUPH5fLZv3z7r27eve2xuv/12GzdunPXu3dt9j9SyZUsbO3asu82pgMIQUEatX7/e7r33XouKirKqVavaBRdcYOHh4VajRg0bP368180rt/wn759//tm6d+9ujuNYq1atbM6cOe46GRkZ9uWXX7ovCPfee6/t27fPqyaXO1lZWfa///3POnbs6L64fvPNNwHrbNu2zeLi4sxxHHv44YfN7NR54S2Pvv32W4uIiLCbbrrJnnnmGXMcx8LDwy0hIcHrppVL+/fvt4suusg9x3Tr1i3gHJSVlWVZWVketrD8859PBg0aZI7j2G233eYuy8zMtCNHjpiZuceBIl3x2bBhg11zzTXu8//888+3efPmucsTEhLcHqSzZs0yM/IvCVOmTLEqVapYrVq17H//+1/AsvT0dBs0aJCFh4db79696TldAj744AOrUqWK1a5d27788suAZX/88Yd17tzZwsPDzXEcmzJliketDE6ffPKJVa1a1Ro3bmw///xzwLK5c+davXr13B7uuc9dXqMwBJRRjz76qDmOY7Vr17b//Oc/tnTpUjvrrLPcniurVq0yM94MlYTU1FS79tprLSQkxG666Sbbv3+/uyw7O9vNfNiwYW7h4tChQ141t9zZvHmzXXLJJW7RJ/dzPPcH4jvuuMMcx7GePXt61dRyz//heP78+eY4jo0ePdq+//57a9eunTmOY9dffz3noBKwYsUKq1mzpttLJbeCPoBxHIpfSkqKNW/e3E477TRbsGCBmQXm7/P5GNJXArp06WKO41jDhg3tv//9b8Ayf1HO/35o6NChHrWyfNu8ebOdccYZ5jiO/d///Z97e1ZWlvu68N///tccx7HLL7/cq2aWW3v27HGHrz722GPu7bnfA33yySfWpk0bcxzHqlWrZsuXL/equUHH/8XNgw8+aGY5xyX354Pp06e7Pds7dOhgmzdv9rK5LqaLB8qoVq1aSZKGDRumu+66S+3atVO/fv0UGxurxYsX67333pMkrgpRAn755RfNnDlTDRs21ODBg1WlShVlZ2dLysnbP8HixRdfrNjYWK1Zs0YbN270ssnlSmhoqJo3b66hQ4fqkUceUUhIiJu/4zgKDQ3Vpk2b9Msvv0iSrr32Wklcqawk+J/r27dvlyRFRkbq3HPP1Y033ihJmjFjhv73v/951r7yqmnTphoxYoQk6dNPP9XmzZsl5VyVr0KFCpKkhIQE/fDDD5o1a5Y2btyojIwMSfwdFKetW7dq7dq1qlWrljp27ChJbv4zZszQ3/72N91yyy3q2rWrxowZ456TOAYnxn+ef/rpp/XYY49p2bJluuOOOwKWhYWF6dChQ4qKipLjONqzZw9XJisB+/fv1x9//KGQkBBdffXVkv48Bv4rLq1cuVL16tXTAw884FUzy61du3ZpxYoVCg0NdfP3+XwKDQ113/dfccUVatOmjUJDQ7V37149//zzOnDggIetDg579uzRypUrFRoaql69eknK+WyQ+/PBFVdcoXvuuUdVqlTR0qVLNXnyZKWlpXnZbElcrh4os2666SZt3LhRDzzwgHvVmT59+qh79+5KTk7WZ599pnnz5kk6dS+LWFZ169ZNDz74oF5++WWdddZZknKKFX7+E39SUpJSUlJUvXp1rkpTjGrXrq3BgwfrkUceUXx8vCS5xSH/G6JPPvlEiYmJaty4sf7yl79o586dXBGlBPjPLWFhORc5jY2NVUhIiC699FJddtllknI+xB06dMizNpZHERERuvzyy903nYMGDXJvX7hwoa644gpdfvnl6t27t6644gp17dpVvXr10rJly9wPbzh5y5cvlyTFx8crMjLSve2CCy7QDTfcoA8++EAfffSR5s+fr2HDhqlnz56aN2+eMjMzvWx2meV/nb300ks1YsQIVaxYUdnZ2TKzgNfgqlWrql69ejIz7d69W1FRURTjillWVpYaNmwon8+nmTNnSso5Pv5/3333nWbMmKHQ0FAtWbJE06ZN0+rVq3XkyBGPW14+7NmzR3FxcQoPD9e6desk/flFsOM4ysrKUkhIiK688kr3nP/OO+/ohx9+8KzNwSI2NlbVqlVTdna2fv/994Bl/vehkZGR6tmzp+68805J0sSJE93XEy9RGALKKMdxdPrpp8vn87n/IiIidO+996pmzZpavXq1pk6dqsOHDyskJIQ3RcXE/wL71FNPqXv37rKcIbkB6/g/LPuzr1SpEkWJYtaoUSNVqlTJ/d3fU2j79u3629/+psGDBystLU0+n0933nmnOnbsqPvuu0/ffPONh60uf/xvRP/44w9Jct/0t2nTRtdff73i4uK0ePFi/fe///WsjeVV7dq11a9fP0VEROjTTz/VJ598oqeeekrnnHOOZs2aJSmnYBEfH699+/Zp3rx5GjhwoD744AOPW172+c/5DRo0UFhYmNauXav169dr9+7d6tOnj3744Qe1bdtWkydP1vjx43X11Verdu3aOnDggIYOHaqvvvrK40dQPvh7SOR+ffW//p599tmSpAULFmjz5s28BhezWrVqqXbt2goJCdF//vMfTZ06Vb/++qvWrl2rfv36qXv37tq8ebN27Nih0aNH68Ybb9QFF1yge++91+29iBNXs2ZNJSUlKT09Xdu2bcvTK87/ZY3/Ndr/fmnSpEml29By6lifqfbt26fY2FiFhoZq3bp1Onz4cL7nn9NOO01/+9vf1LJlS+3fv19vvvmmJG+/zKcwBJRx/u6J/pN/jx49dPXVVyszM1Nz5szRJ598Ikm8KSom/m8lo6OjFRkZKcdx8mTrf8FYu3atfD6f2rZtq5o1a5Z6W4PNf/7zH9WtW1fvv/++IiMj1b9/f/Xs2VO1a9fWzp07NXHiRPXp00cLFy6URE+64uB/rlesWFGS1LhxY0k5PVe6d++ua665RpI0evRoHThwQOnp6Xr55Zf13XffedPgciQkJERdu3Z1h9L07dtXTzzxhKpUqaKxY8dq9erVmjdvnr777js988wzkqSFCxfqpZde0m+//eZl08s8/zk/OztbtWrVks/n04IFC/TKK69oxYoVGjp0qJYuXaq7775bAwcO1Pvvv6/x48crNDRUixcv1rRp07RhwwaPH0XZl99Qef9t/v+rVaum/fv3l2q7yjszU+3atXXHHXeodevWysjIUL9+/XTJJZeoRYsWev311xUSEqJhw4bpxRdf1MMPP6x27dopKSlJ//3vf/X8889r3759Xj+MMsvM1Lx5c1133XWSpPfff18rVqxwl2dlZbk/f/nll3IcR8OGDVNoaKi++OILLVq0yN0PCi8lJUUffPCBjhw5IsdxCsyvbt26qlOnjrKzs7VmzRrt2bOnwH02a9ZMt9xyiyRp8uTJ2rhxo7df5pf2pEYASo5/UrOVK1daixYtLCQkxHr37m1bt24NWI7ScfXVV5vjOPbcc8+ZmXGVoBL2z3/+00JCQuyBBx6wXbt2mdmfmf/3v/+1Dh06mOM49pe//MXLZpZLDz/8sDmOY++99557W3Z2tn388cfWpEkTcxzHevToYXXq1HEvq8v5qHj89NNPVq1aNTdX//n+aE899ZRFR0dbdHS0PfLII6XcyvIpNTXVndz17rvvtnr16lnLli3t4MGDZpYzEbLZn6+9TzzxhDmOY3Xr1rVPP/3Us3aXZ/6Jj9euXetetcx/1R/OOcXDn3FmZqYtWrTIrrvuOmvQoIG1bNnSPdevXLkyYJvff//dBg4caI7jWKNGjWzu3LleNL1cmTVrlkVFRZnjONanTx93Any/yZMnW2xsrPXq1cuWLFniTog8YsQIj1pcdk2cONHCwsKsSpUqNm3aNDPL/0q3/vecX3/9tXv++fjjj4+577lz57oTiT/66KPF3/gioMcQUI74vyFr1aqV+vTpo7CwMC1YsEAffvhhwHKUvG3btmnp0qUKDQ11JyXNPQcCio+/58+TTz6pNWvW6MUXX1T16tUD5hy66aab1LNnT0VHR+unn35iKEcxyT1fTUhIiJo0aSIpZ0hZSEiIevTo4Q7pmD17trZv364zzzxTjz/+OOejYtKuXTsNHjxYV155pV577TX3m0r7/984+r89vu+++3TGGWfo8OHDWrt27TG/xcTxZWdnKzo62p0j4v3339fWrVvVqVMnd9jG0cM5/v73v6t27dratm2b+w2/8a19sfL35goJCVG7du0kye2hyDmnePgzDgsLU+fOnfXhhx9q3bp1Gj58uOLj4/XQQw+pVatW7jQHknTGGWfooYceUq1atbRx40bNnz9fEs//k3HRRRfpwQcflCS99957uuqqq9S3b189/vjjOvfcc3XPPfcoJiZGQ4cOVbt27dy5Lv2Z02v6+DZt2qQBAwbo3nvvVXZ2ttLT0zVt2jTt3r1bjuPkydD/Pr9169bq0aOHJOnf//63UlNTC7yPli1bqnr16nIcR7t27fJ0EmrOkMAp5mRP1P7t+/Xrp7POOkt79+7VRx99pKVLl0oSk14ex8nm73/BXblypTZt2qTGjRurS5cu7vLcXXyR14nk73+zHxYWpqZNm8rMAuaeMDNFRUXpnHPOUUZGhmJjY/k7KEBR8/e/Cdq7d698Pp8OHjwoKWco2datW3X//fdr6tSpknL+Ntq3b6+ff/5ZHTp04ANBAYp6DKKionTbbbfpueee02mnnSZJAfOuhIWFKTs7W1WqVFHv3r0l5Zyf/MP/EKiw+fvPO/3791eDBg3cN/7+7Y8+1/t8PkVGRrrFiuXLl8vMGOZ9lOL6sFq9enUlJyeXyL7LsxN9DV63bp0GDBigevXq6dJLL3Vv9/+dmJlq1KihM888U1LOFwU+n4/n/1GKkn9ERISeeeYZ3XbbbapcubL27dunt99+W6NGjdJPP/2kTp066a233tIFF1wgx3HUtm1bSXKHElMoPbYNGzboueee05QpU3Taaafp/PPPV1ZWlr7//ntNnz5dUsEZVq1aVddff70iIyM1b94890rRR/P/XbRu3Vpmpk2bNrkXFPICzwjgFJP7RfREt/f5fKpatar69++vSpUqacmSJe6Hs/DwcB08eFBjxozRG2+8UWztLi9ONn+/BQsWSJL+8pe/KC4uTunp6ZL+/AZ5xowZmj179kndR3lUHPk7jhPwYu1/49myZUvFxMQoJSXFvaQ0AhU1f/83wqmpqQoPD1f79u0lSaNGjVKTJk30+uuvS5IuvvhihYSEaOnSpW4PRj4Q5O9E/gbq1q2rZs2aHXefGRkZ7hVrkpKSTq6h5VRh8/cXnaOjo/X444+7t3/xxRdKTExUWFhYwIe8kJAQVaxYUXv37pWUM0H7seapCFbF8Rrg8/kUFxen888/XxI9horiRPPfsmWLDhw4oMjISHdOp9z7cBxHFStWdHsqtm7d2n2/ij8VJX9/j90JEyZo1qxZevLJJzVgwAANGzZMb775pn7++We314ok97h06NChBFpe/rz++uuaOHGiqlSposcff1z//ve/1bFjR+3atUvTp0/X6tWrJeVfzAsPD9cFF1zgzrM4atQo/frrr3nW8x9n//yMqampSk9P9+x1gTMkcIp5//33dc899yglJeWETwz+D1y33HKLLr74YqWlpemrr77S7Nmz9cknn6hz584aNmyYRo4c6X7Djxwnm7/jODpy5Ih++uknSXKHkfkLEfPmzdN5552nG264QS+88IJSUlKKr/HlwPvvv6/+/fuf1PM/N3/hIjs7Wy+//LKSkpJUt25d91t7BCpq/v5vhA8cOKDw8HCNHTtWLVu21KOPPqojR47ouuuu09atWzV16lRdddVVkqSRI0e6H46RV3G8BuTm/xtISUnRzz//LDNTly5d3GEFCFSUvwH/a+3tt9/ufgBLTU3VuHHj3OVm5n6AW7FihTZv3ixJ7rBLCqSBiuP5HxISoqysLFWrVk1hYWFKTk52c8exnehrsH8ydZ/P5w6T9A+18X9w/u6779xl/uc/xbpARck/98VQunTpon/961+aMGGCnnrqKd12220KDQ1VRkaG+0WA/8IbnTp1KvHHUR60bt1akjRs2DDdddddateunfr166fY2FgtXrzY7QVU0HO4cePGuuOOO1S/fn1t3LhRY8eO1eLFiyXJfV/qN2/ePEk5U4FUqFDBu9eFkpzACEDRLF++3J2szD957onyT3o5f/58q1+/vkVERFj9+vXd/bdp08a+++674mh2uVFc+a9du9aqVKlikZGRtmrVKjMz27Bhg/Xt29fdf/Pmze3HH38srqaXCytWrCi257/Zn38DZmZz5syxhg0b2mmnnWavv/76Se+7PDrR/A8dOmQ33HCDhYeHu9u3b9/evv32W3edw4cP29tvv23R0dHmOI5Nnjy5BB5B2VecrwFmgRPev/322xYTE2M1a9Z0J37Nb/LMYHYifwP+CY03bNhgjRs3trCwMHMcx959911LS0tz1/vjjz/cyV/vuusuJkLOR3E9//3P6xdffNEcx7EzzjjD9uzZU1zNLLdO5Pmf32Tf9957r/3+++8B661du9bOP/98cxzHbrrpJktPTy/29pd1xfEeyOfzmc/ns+zsbMvIyHBvf/311y0sLMxat25te/fuLa4ml2s+n88SExMDbsvIyLCrrroqz/ucgs7n6enpNmnSJHMcxyIiIqxnz562du3agHW++eYbq1WrltWrV89++umnEnkshUVhCDiFrFy50s444wyrV6/eSRVtcr/Z3717t3s1JsdxLC4ujg9lBTjZ/P0vDO+//757haCkpCR75plnLDY21s1/4sSJxd30cuFk88/vQ25aWpo99thj7vN/4MCBfEAowMnkP378eAsPD7fIyEh75ZVXApYdOXLEzHI+ON9///02ZsyYYmtzeVNSfwOPPvqo+zfw9NNPc4XEApxo/v5z/8yZM+2yyy4zx3GscuXK1r17dxs+fLj179/fLZx26tTJFi5caGYU5o5W3O+BfvnlF6tUqZI5jkMxtBBONH9/pvfcc485jmOxsbHWpUsXe+GFF+yjjz6yQYMGueefzp072/z5882Mq8Qdrbie/7mlpqbaP//5T3Mcx6pWrWpffPFFsew3mGRnZ7v/zMy++uorq1WrlkVERNjdd99tqampZnbsc8s999zjnotatWplgwYNsvfee8/uvPNO92/jwQcftLS0NE/PURSGgFJ0vD/2nTt3Wnx8vDmOYz/88IOZnfgL5+HDh+25556zKlWquCedv//975aSkuKuk7tHRTAorfzvv/9+99uEZs2aufkPGjSI/I+huPJfsmSJrVixwsaMGWO1atVy83/yySeD+lvKksjfv8/du3fbG2+8cdznd+79BeOHgtL6G1i6dKktX77cxo4da3Xq1HH/Bp599tmAb5GDTUnln3u/W7dutWuvvdZq1qzp5u7/tvihhx4K6EUUbErzPZCZ2U8//WS1a9c2x3HsjTfeOOH9lBclnf++ffusZcuWFhISEvC89//88MMP8/w/huJ6/q9atco++OADGzVqlFWvXt0cx7HIyEh76aWXgu59Z0kZOHCgOY5jDRs2tPfee6/A9fxfwhw4cMAmTJhg1apVC3hdcBzHYmJi7IUXXiillh8bhSGgFPh8vjzf0B59svf/fs0115jjODZixIgTvr+kpCTr3bu3e9Lp1auXrVmzxl2emZkZVN+alWb+Bw4csHPPPTfgjVGPHj3Iv5TynzlzpjmOE/Die91119lvv/1W4H2Xd6V9/jne89vf1T2YlOYx+PTTT81xHKtRo4b7N9C7d2/+Bkohf//zOjk52VavXm3PPfecjR8/3saPH28bN2501wu2HlulfQ7yO3jwoEVGRlqnTp1sx44dJ72/sqo08vfvf/ny5TZy5Ehr27attW3b1i6++GK77777bN26dXnWDRal/fyfO3duQOHhmmuuCRi+FGyvv8XJf5xWrlxpLVq0sJCQEOvdu7dt3bo1YHlBVq5caa+99prdeuutNnDgQHv66adt9+7defbvlTBvZjYCgovjOAoNDdXmzZs1e/Zs3XXXXXkmKwsJCVF6erri4uIk/TnLvc/nK/LkfBUrVlSDBg3Url07Pf300+rVq1fAPv1XxgoWpZl/5cqVtXXrVpmZmjZtqueee86ddJf8Sz7/q6++Wt27d1dERIRatWql6667Tuecc07APoNtssvSzN/Mjvv8DsbJdkvzGFx55ZXq2rWrDh8+rN69e+vWW29V165dA/bJ30DJ5O9/bkdHR6tFixZq0aJFwHL/Pv2TxgaL0n4P5FepUiUtW7bsmFfsCwalkb//Od2mTRu1adNGAwYMUFRUlJKTk1WtWrWAffL8L9nnf/fu3TVy5EgdPnxYl19+uc4666yAfQbb+b84+bNr1aqV+vTpo+HDh2vBggX68MMP9eCDDx4325YtW6pVq1a66667lJ2d7f4tZGdnuxfz8FJwfToBPPTee++pT58+8vl8+u233zRo0CDVq1dPZuZeuaRChQqqX7++JGnJkiWSin4C959onn322YBLcuc+AQWj0sg/KytLYWFhevPNN/Xjjz/qX//6l7uM/Esv/5kzZyojI8N9MyqRf2mdf4Kx6FNYpfk3MH36dB06dMi98o/E30Bp/Q0cvY3/g52Zef6m30ulmX9u/qKQ/28jWJV2/lWqVJH05xVZT6bAVx6UVv7+/Q0ZMiTg+R7s5//cTva56N++X79++uKLL/Tjjz/qo48+Uvfu3dW+fXtlZmYqPDw8YBv/ccn9HsnfBp/Pd8ocm+D9CwVKWatWrfTYY49JksaOHas+ffpoxYoV7knCf9nCs88+W+Hh4UpMTNTWrVuLfD/+k4v/xdi/31PlpOOV0sjf/yLcrVs3tyiUlZUlifxLM/+4uLig/4byaKV1/kHBSvNvoFq1am5RiL+BHF79Dfjf/Ad70dTrc1AwF4Uk7/MP5qKQVHr5+/fnf75z/s/L/1w0sxPe3ufzqWrVqurfv78qVaqkJUuWaOrUqZKk8PBwHTx4UGPGjNEbb7xR4H35j9Up9bdRaoPWgHLOf3nI3PIbKzpmzBhr27ate8n4Dz/8MGC5f46U1q1b26FDh0q0zeXJqZh/MI3jPhXzDybk7z2OgbfI31vk7y3y9xb5lx3vvfee9e/f35KSkk74fbp/O5/PZ9dff717PL/++mv7+OOPrXHjxuY4jjVp0sQOHDhQjK0vWRSGgGKQe5b/lJQU2717tyUnJweccPzrHDlyxFauXGmtW7c2x3GsUqVKAZcP3rdvn3tJwwULFphZcBUYTgT5e4v8vUX+3uMYeIv8vUX+3iJ/b5F/2bF8+XJ3Uu5du3ad1L78x3T+/PlWv359i4iIsPr167v7b9OmjX333XfF0exSQ2EIOAlHfxvw8ssvW9euXa1z587WvHlzGzBggCUkJLjLc3+jsGzZMhs8eLB7Ahk4cKD99ttvlpGRYV27drWoqCj74IMPSvXxlDXk7y3y9xb5e49j4C3y9xb5e4v8vUX+Zc/KlSvtjDPOsHr16p1U0SZ3sW737t3WoUMH91jGxcXZ5MmTi6O5pY7CEFAMvv76a2vZsqV7UqhVq5b7c8OGDW3q1Klmlv8lOocPH241a9Y0x3HsL3/5i/3yyy925plnmuM47okl2C7tWVTk7y3y9xb5e49j4C3y9xb5e4v8vUX+p47j9a7auXOnxcfHm+M49sMPP5jZiV8i/vDhw/bcc89ZlSpV3OP997//3VJSUtx1cvcmKwsoDAEnISkpyUaNGmURERHmOI51797dvvnmG1u9erV9/vnnNnToULer6KZNmwK29Z+8MjMz7auvvrImTZq4LwwNGjQwx3Hsb3/7mxcPq8wgf2+Rv7fI33scA2+Rv7fI31vk7y3yP3X4fL48BbSC5ny69tprzXEcGzFixAnfX1JSkvXu3dstCPXq1cvWrFnjLs/MzCyTQwApDAEnYcqUKRYdHW1VqlSxN954I8/yzZs3W8OGDc1xHLvxxhuPua+EhAS74YYb3JOM4zjWt29fy8zMPOFqdnlH/t4if2+Rv/c4Bt4if2+Rv7fI31vkf+rZtGmTvfbaawUuP3z4sPXt29ccx7EnnnjCzE68x9ADDzxg7du3t88++8y9LTs7u0wfLwpDwHHkV/HNysqy5ORku/32261WrVr2008/5Vln3LhxFh0dHXCS//zzz495X8nJyfbggw+6E881a9as2B5HWUX+3iJ/b5G/9zgG3iJ/b5G/t8jfW+Rfdrz77rsWGhpqjuPYkCFDbPPmzWYWeAUxM7PHHnvMHMexq6666oTux98z6fDhw/neXpZRGAIKafr06fbiiy8G3PbZZ5/ZjBkzLD093b1tzpw51qpVK/eFoF+/fnbrrbea4zh2zjnnWHJy8jHvJysryyZOnGjVq1e3OnXq2LJly0rk8ZQ15O8t8vcW+XuPY+At8vcW+XuL/L1F/qe+ZcuW2fDhw93sL7jgAlu+fLm73D/fz+eff24RERHWtm1b27Jly0nfb3koCPlRGAIKYebMmeY4jrVs2dL27t3r3p57UrFDhw4FnJDOP/98+/HHH83MbPXq1e43AC+//HKB9+OvZi9evNji4+OtatWq9vvvv5fQoyo7yN9b5O8t8vcex8Bb5O8t8vcW+XuL/E8Nua/q5pffsK0xY8ZY27ZtzXFyLhn/4YcfBiz3H8/WrVvboUOHSrTNZQ2FISCXgsaFzpo1y2rUqGHt2rULuPRkbp988onVrFnTKlSoYGPHjg1Ytn79ervwwgvNcXKuVnD0JHT5adasmTmOYzNnzizy4yiryN9b5O8t8vcex8Bb5O8t8vcW+XuL/E9duYtwKSkptnv3bktOTg4Y6udf58iRI7Zy5Upr3bq1OU7O5N9PP/2027Nn3759bqFuwYIFZnb8q5kFCwpDgOW8GBQ0jtjMbNeuXe5VAvyTjOXuOnjw4EF3+TPPPOPefuTIEffnFi1auN8kDBkypMB2ZGdn26FDh9wTmv8bh/KM/L1F/t4if+9xDLxF/t4if2+Rv7fI/9R1dLHu5Zdftq5du1rnzp2tefPmNmDAgIBiXe5eRcuWLbPBgwe7uQ8cONB+++03y8jIsK5du1pUVJR98MEHpfp4TnUUhoBcvvzyS7vtttvs+++/d2/zn/wHDRpkjpP/lQWWLVtmMTExdvrpp1tiYqJ7u//FZuvWrdawYUM799xz3RPU/PnzC2zHnDlzrEqVKhYfH2+//fZbMT7CUxv5e4v8vUX+3uMYeIv8vUX+3iJ/b5H/qevrr7+2li1buvnVqlXL/blhw4Y2depUM8t/vp/hw4dbzZo1zXEc+8tf/mK//PKLnXnmmeY4jk2ePLnA7YIRhSHAck7eL730knuS6d69u82dO9fM/qz4f/rppxYVFWUtW7Z0x/z6q9JbtmxxZ8JfuHChmVnAZHQTJkwwx3Hsm2++sW7dupnjOPbzzz/naUdaWpqNGDEioLqdkZFRoo/9VED+3iJ/b5G/9zgG3iJ/b5G/t8jfW+R/6kpKSrJRo0ZZRESEe2y++eYbW716tX3++ec2dOhQc5yc4WJHD9Hz9wLLzMy0r776ypo0aeIWh/w9vP72t7958bBOWRSGEHQKGkP83HPPmeM4FhkZaY7jWOPGjW3r1q3u8h9//NEaNWpktWvXtm+++SZg2/3799tNN91kjpMzC35SUpKZ5VSgR44caaGhoda1a1fz+Xy2bdu2Atu2d+9e6969u0VGRtq4ceOK4dGeesjfW+TvLfL3HsfAW+TvLfL3Fvl7i/zLlilTplh0dLRVqVLF3njjjTzLN2/ebA0bNiywN1duCQkJdsMNN7iFN8dxrG/fvpaZmVng8yLYUBhCUMlvNnt/98Ft27ZZeHi4NWnSxLp06WKO49hFF13kdinNysqyzp07m+M4NmHCBDP7c6Kz7Oxs++CDD9yuik2bNrXLL7/c6tata47jWHx8vE2fPt3M/qxg5x57nPv2xYsX2/79+0soAW+Rv7fI31vk7z2OgbfI31vk7y3y9xb5n5oKmt8pOTnZbr/9dqtVq5b99NNPedYZN26cRUdHBxR6Pv/882PeV3Jysj344IPu5NPNmjUrtsdRHlAYQtD57rvvbPDgwQEz3GdlZVl2drZdccUV1qhRI3v99dft9ttvt9DQUDv77LPtl19+MTOzsWPHmuM4ds4557jb+l9oDh8+bJMmTbKYmBhzHMfCwsLMcRy78MILbenSpaX7IE9h5O8t8vcW+XuPY+At8vcW+XuL/L1F/qeu6dOn24svvhhw22effWYzZswIGJo3Z84ca9WqlVsM6tevn916663usUlOTj7m/WRlZdnEiROtevXqVqdOHVu2bFmJPJ6yiMIQgsrKlSvdE8kDDzxgq1evNrOcanVmZqb961//MsdxbMqUKbZnzx676667zHEc69Chg+3atcu+++47q127ttWuXdutXh9d6V6yZIm988479vLLL9sXX3zh3u7z+YL+cojk7y3y9xb5e49j4C3y9xb5e4v8vUX+p66ZM2ea4zjWsmVL27t3r3t77gLeoUOHbPjw4e4xPP/8892rtq1evdrtBfTyyy8XeD+5e2bFx8db1apV3TmjQGEI5ZT/Dz+/k/Cbb75pUVFRVqFCBbv++uvt0KFD7rIZM2aY4zjWrVs3M8v5JuCyyy4zx3HshhtusNdff926du1qMTExNm3atHzvMz+5T2zBgPy9Rf7eIn/vcQy8Rf7eIn9vkb+3yP/UVdBcPrNmzbIaNWpYu3btAi4/n9snn3xiNWvWtAoVKtjYsWMDlq1fv94uvPBCc5ycK5YdPRF1fpo1a2aO49jMmTOL/DjKKwpDKFeys7MLPOnkvn3MmDHWqFEjcxzH7rjjDktJSTGznJN33bp1LT4+3r1iwJo1a+zmm2+2kJAQu/DCC61169bmOI499NBDZnbsSxwG27cD5O8t8vcW+XuPY+At8vcW+XuL/L1F/qeu7OzsAucSMjPbtWuXe6Wwzz77LGCZmdnBgwfd5c8884x7e+65mlq0aOH2JhoyZEiB7cjOzrZDhw65x9Lf6wgUhlCO5D6BbNiwwd5++2378MMPbebMmbZjx46AE9KhQ4ds6tSp7gnk4YcftrVr15qZWb9+/axChQr2wQcfuOtv3LjRncneP364QYMGAd80BDvy9xb5e4v8vccx8Bb5e4v8vUX+3iL/suHLL7+02267zZ3U2+zPYzdo0CBznPyvLrZs2TKLiYmx008/3RITE93b/QWnrVu3WsOGDe3cc891j+v8+fMLbMecOXOsSpUqFh8fb7/99lsxPsKyjcIQypXk5GQbPHhwnlnqW7ZsaSNHjsyz/vPPP28REREWFRVlN998s/l8Phs/frw5jmOPPvqomf35LUNSUpKdd9557otCVFSUW9VGDvL3Fvl7i/y9xzHwFvl7i/y9Rf7eIv9TV3Z2tr300kvuMenevbvNnTvXzP7s9fPpp59aVFSUtWzZ0p33x5//li1bLDQ01BzHsYULF5qZBUxIPWHCBHMcx7755hvr1q2bOY7j9vrKLS0tzUaMGOG2Y+DAgZaRkVGij70soTCEMs//LcBXX33ljhcNCQmxPn362MCBA+2CCy6wuLg4cxzHRo8enWfbkSNHWp06dcxxHBsxYoTNnj3bHMexxo0buycr//8JCQl23XXXud1IGTdM/l4jf2+Rv/c4Bt4if2+Rv7fI31vkf+opaDjfc889Z47jWGRkpJvx1q1b3eU//vijNWrUyGrXrm3ffPNNwLb79++3m266yRzHsQsuuMCSkpLMLKe30ciRIy00NNS6du1qPp/Ptm3bVmDb9u7da927d7fIyEgbN25cMTza8oXCEMqFnTt3ut0Hr7rqKtuwYUPA8unTp5vjOBYXF2f/+9//ArqUHjx40F5//XW3evyvf/3L6tSpY1WrVnWvKJB7/Q0bNtjmzZvd3xlDTP5eI39vkb/3OAbeIn9vkb+3yN9b5H/qOLoolJ2d7Q4V27Ztm4WHh1uTJk2sS5cu5jiOXXTRRe6wsqysLOvcubM5jmMTJkwwsz8n7s7OzrYPPvjAatasaY7jWNOmTe3yyy+3unXrmuM4Fh8fb9OnTzezP49J7vmHct++ePFi279/fwklULZRGMIpK3cXQb+CJnnzj0sdMGBAnmUbN260e++91z3p9+vXL999P/rooxYZGWkxMTEWHR1tUVFRNmnSpALbl5WVVWBVvDwgf2+Rv7fI33scA2+Rv7fI31vk7y3yL7u+++47Gzx4cECPKn9eV1xxhTVq1Mhef/11u/322y00NNTOPvts++WXX8zMbOzYseY4jp1zzjnutv6cDx8+bJMmTXKH84WFhZnjOHbhhRfa0qVLS/dBllMUhnBKWrBggQ0aNMhWrFhhZjljQv1yn4yzsrIsNTXVWrZsaVWrVg0YT3rkyBEbNWqUVapUyRzHsdjY2Hy7Dfr3lZycbCNGjLAqVaq4LyDXXXddST7MUxb5e4v8vUX+3uMYeIv8vUX+3iJ/b5F/2bVy5Uo3vwceeMBWr15tZjm9dTIzM+1f//qXOY5jU6ZMsT179thdd91ljuNYhw4dbNeuXfbdd99Z7dq1rXbt2vbTTz+52+a2ZMkSe+edd+zll192e3X516MH18mhMIRTzuHDh23UqFHmOI61b98+YNmrr75qZ511lk2ZMsW9bffu3RYVFWWtWrVyb5s2bZq1atXKPTkNHDjQDh486C4/elyw/0Ry8OBBGzduXMA3C8F2kiF/b5G/t8jfexwDb5G/t8jfW+TvLfIvG/y55JfPm2++aVFRUVahQgW7/vrrA67eNmPGDHMcx7p162ZmOYW5yy67zBzHsRtuuMFef/1169q1q8XExNi0adPyvc/8MN9T8aAwhFPS119/7Vbt//e//9maNWvccaeO49i///1vt8qfkJBgsbGx1qFDB/vuu++sZ8+e7noXXnihLV++3N3vkSNH8pw88usKOnLkSFu2bFnJPshTGPl7i/y9Rf7e4xh4i/y9Rf7eIn9vkf+pKzs7u8AhdLlvHzNmjDVq1Mgcx7E77rjDUlJSzCyngFO3bl2Lj493e3itWbPGbr75ZgsJCbELL7zQWrdubY6TM8G3WcFDCM2Y46m4URjCKenIkSM2evRoc5ycS0L6T/LnnHNOQLdBs5wThn8yMv9409NPP90++ugjd53ck5+Zme3YscNefPHFPPd79Mnn6O2CBfl7i/y9Rf7e4xh4i/y9Rf7eIn9vkf+pKXcWGzZssLfffts+/PBDmzlzpu3YsSOgSHPo0CGbOnWqe+wefvhhW7t2rZmZ9evXzypUqGAffPCBu/7GjRvthhtuMMdx3DmEGjRoENDbCCWPwhBOWQMGDLCQkBCrUKGCxcXF2XvvvRdw0sl9wh4yZIj7ovDUU08F7Me/jv//ffv22R133GEhISE2c+bMAu8/2KvQ5O8t8vcW+XuPY+At8vcW+XuL/L1F/qem5ORkGzx4sEVHR7tFH8dxrGXLljZy5Mg86z///PMWERFhUVFRdvPNN5vP57Px48eb4zj26KOPmtmfPY2SkpLsvPPOcwtDUVFR9tlnn5Xq4wt2FIZwSnrttdfMcRwLDQ11TzqHDx82M3P/z+2bb76xBg0amOM4Nnr0aDP7c4I6n88X0L1x5MiRFhkZaV26dLFNmzaVzgMqY8jfW+TvLfL3HsfAW+TvLfL3Fvl7i/xPLf4i2VdffWXNmjUzx3EsJCTE+vTpYwMHDrQLLrjA4uLiAvLPve3IkSOtTp065jiOjRgxwmbPnm2O41jjxo3dS8r7/09ISLDrrrvOHUrG3EGli8IQPHWsycuGDRtmX3zxhfXu3dscx7Hrr7++wP3s3bvXnnjiCfdk9cILL9iOHTsC1lmxYoX16NHD7WaauwtjsCJ/b5G/t8jfexwDb5G/t8jfW+TvLfIvO3bu3GnnnnuuOY5jV111lW3YsCFg+fTp081xHIuLi7P//e9/Acf04MGD9vrrr7tFvn/9619Wp04dq1q1qjs0MPf6GzZssM2bN7u/03ur9FAYgieOVQE+etmMGTMsIiLCHMexb7/9tsDtt27d6r6AREZG2plnnmlDhw61559/3m6++Wb3hNSqVSubPXt2sT6esob8vUX+3iJ/73EMvEX+3iJ/b5G/t8j/1JCenp7ntoLmVBo0aJA5jmMDBgzIs2zjxo127733uhn369cv330/+uijFhkZaTExMRYdHW1RUVE2adKkAtvn7/WF0kNhCKXq6BPO9OnT7bnnnrNnn33Wpk6dmu8kY/v27bP+/fub4wReujK/CvKePXvstttus4oVKwaMfXUcxypWrGiPP/54QBuCrQpN/t4if2+Rv/c4Bt4if2+Rv7fI31vkf+pYsGCBDRo0yFasWGFmZmlpae6y3AWZrKwsS01NtZYtW1rVqlXdK4mZ5Qz/GjVqlFWqVMkcx7HY2FgbN25cnvvy7ys5OdlGjBjhXnHOcRy77rrrSvJhoogoDKHU5D4Bf/HFF+7lCHP/u+iii+yHH34ws8DLHs6fP98dnzp58mQzC/zG4OgxxAsXLrRXXnnF7rzzTnv66adt/PjxAd1Kg3HMKvl7i/y9Rf7e4xh4i/y9Rf7eIn9vkf+p4/DhwzZq1Kg8xTYzs1dffdXOOussmzJlinvb7t27LSoqylq1auXeNm3aNGvVqpV77AYOHGgHDx50lx+dsf/4Hzx40MaNGxfQuyiYC3SnGgpDKDaF+cPeuXOn2x3Rf0J68sknrX///nbeeeeZ4zjWqFEj27JlS8B2qamp9uSTT5rjOFa5cmVLTU01s5wTT1EuJVmeuyWSv7fI31vk7z2OgbfI31vk7y3y9xb5ly1ff/2123Pnf//7n61Zs8Y6d+7sHpt///vfblYJCQkWGxtrHTp0sO+++8569uzprnfhhRfa8uXL3f0eOXIkT1Eov8xHjhxpy5YtK9kHiSKjMIRilZWV5c4sf/SJYOfOnXbXXXeZ4zhWo0aNgGq0X8eOHc1xHLv77rtt//79AcvWrFljXbp0McfJmak+t7S0NHvnnXds5cqVee7b/3MwVKTJ31vk7y3y9x7HwFvk7y3y9xb5e4v8y44jR47Y6NGjzXFyLgvvL/Scc8457oTQfllZWVazZk1zHMfCwsLMcXIm8P7oo4/cdbKzswOKdDt27LAXX3wxz/0eXcg7ejt4i8IQTtp3331nEydOtGHDhtmll15qL730Ur7rTZkyxRzHsWuuucZ2796dZ/lbb73ljgsOCQmxadOmBZwsjhw5Ym+88YZ78po2bZodOHDAPvzwQ2vZsmW+LxbBgPy9Rf7eIn/vcQy8Rf7eIn9vkb+3yL/sGjBggIWEhFiFChUsLi7O3nvvvYACWu6izZAhQ9zC0FNPPRWwH/86/v/37dtnd9xxh4WEhNjMmTMLvH+KdaceCkM4YfPmzbMePXpYhQoVAsYIn3HGGTZ//vw868+ZM8euvPJKW7RoUcDtc+fODRhrfMYZZ5jjOHbppZfaxo0bA9bdv3+/3XrrrW53Uv+YY8dx7JJLLsmzfnlG/t4if2+Rv/c4Bt4if2+Rv7fI31vkX7a99tpr5jiOhYaGuhkePnzYzMz9P7dvvvnGGjRoYI7j2OjRo83sz6F5R8/xNHLkSIuMjLQuXbrYpk2bSucBoVhQGEKRbd261W677Tb3RNK4cWN78skn7dVXX7V58+bZwoULbf369Xm2S0lJCZj8bdeuXTZgwAB3P127drVFixbZoUOH3Nv+/e9/57nk4YEDB+ySSy6x+vXrW7Vq1ey8886zL7/80l1e3ivQ5O8t8vcW+XuPY+At8vcW+XuL/L1F/mWLP4/8chk2bJh98cUX1rt3b3Mcx66//voC97N371574oknzHFyenS98MILAcfTzGzFihXWo0cPc5ycoWYffPBB8T4YlDgKQygU/wnlyy+/dLtsVq9e3V555ZUT2t++ffusT58+5jg5Y1snTJgQsPyOO+4wx3GsefPmtnTpUvd2fzfFAwcO2KZNm2zhwoUB25XXcark7y3y9xb5e49j4C3y9xb5e4v8vUX+Zc+xrrx29LIZM2ZYRESEOY5j3377bYHbb9261S0iRUZG2plnnmlDhw61559/3m6++Wa3oNeqVSubPXt2sT4elA4KQyi0xMREu/jii81xHLvxxhvtwIED7rKjJ3fLzMw8ZtV+8uTJFhUVZW3atAmYlf7IkSN25MgRGzRokNu98aGHHrKkpKRjti0YXgzI31vk7y3y9x7HwFvk7y3y9xb5e4v8y4ajs5g+fbo999xz9uyzz9rUqVPt0KFDebbZt2+f9e/f3xwn8PL1+R3DPXv22G233ebOB5X7X8WKFe3xxx8PaAM9uMoWCkM4Lp/PZ4cPH7bLL7/cHMexK6+80rZv325mOSeg3H/0Pp8v4ISwdu3afCeZu/TSS81xHBs2bJiZmTtG1b8v/wnK/63El19+GbQnF/L3Fvl7i/y9xzHwFvl7i/y9Rf7eIv+yI3dGX3zxRcDcTf5/F110kf3www9mFnj1tvnz57tzNk2ePNnMAnsNHT2P0MKFC+2VV16xO++8055++mkbP358wNCyY/VYwqmLwhAKZenSpVa5cmWLj4+377//Pt91cp8E9uzZY/fdd585jmNjx461jIwMd9mBAwfcCcxmzZrlbuu/xOX27dutfv36NmzYMDv77LPNcRy777778owzDibk7y3y9xb5e49j4C3y9xb5e4v8vUX+p4bCFMd27txpgwYNcgtB7du3tyeffNL69+9v5513njmOY40aNbItW7YEbJeammpPPvmkOU7OxN6pqalmlnNsitIjyz8hNcomCkMolKeeesocx7G2bdse9w9+/PjxVqtWrYCxpv6uov6Tmn/iul69euXZ3n9JxNmzZ9uSJUvsH//4R/E/oDKG/L1F/t4if+9xDLxF/t4if2+Rv7fI/9SRlZXlFtGOPhY7d+60u+66yxzHsRo1atiUKVPybN+xY0dzHMfuvvtu279/f8CyNWvWWJcuXdwhfLmlpaXZO++8YytXrsxz30cPJUTZRWEIheKfZb5fv35mlvdkZGa2bNkya9KkifticPXVV1u1atXMcRz7xz/+ETBGePbs2RYTE2OO49gdd9xhkyZNsrfeesud1O7yyy+3lJSUgP0H8xhi8vcW+XuL/L3HMfAW+XuL/L1F/t4if2999913NnHiRBs2bJhdeuml9tJLL+W73pQpU8xxHLvmmmvyHcL31ltvuXMDhYSE2LRp0wJyPXLkiL3xxhvuMZw2bZodOHDAPvzwQ/fYHF0wQvlCYQjH5PP5LD093a655hpznJyxxWlpafmum5CQYI7jWLt27WzBggVmZjZhwgRzHMeqVq1qc+fOdavJaWlp9vjjj7snn5CQEPfnyy67zNasWZOnHcGI/L1F/t4if+9xDLxF/t4if2+Rv7fI31vz5s2zHj16WIUKFdx8HMexM844w+bPn59n/Tlz5tiVV15pixYtCrh97ty5AfMNnXHGGeY4jl166aW2cePGgHX3799vt956qzlOzpAy/7xDjuPYJZdckmd9lC8UhlAol112mXvC3rlzZ4HrLVmyxP3Z5/PZ3r17rXPnzuY4jt1yyy22a9eugPUnTJhgF110kXXr1s26du1q7777bkk9hDKN/L1F/t4if+9xDLxF/t4if2+Rv7fIv3Rt3brVHW7nOI41btzYnnzySXv11Vdt3rx5tnDhQlu/fn2e7VJSUgImgN61a5cNGDDA3U/Xrl1t0aJFdujQIfe2f//733nmbzpw4IBdcsklVr9+fatWrZqdd9559uWXX7rLg7VQFwwoDOGY/H/8r7zyinsSSUhIMLP8u5L6+ce/mpnNnDnT3fbNN9/MMzFZZmamJScnB9wWzF1GcyN/b5G/t8jfexwDb5G/t8jfW+TvLfIvPf6sv/zyS3fYVvXq1e2VV145of3t27fP+vTpY47jWFRUlE2YMCFg+R133GGO41jz5s1t6dKl7u3+7A8cOGCbNm2yhQsXBmwXjMcmmIQIOAbHcSRJDRs2VIsWLSRJo0ePDliWn/DwcPfnq6++Wj169JAkzZ49W47jKCTkz6deWFiYYmNjFRISouzsbElSaGho8T6QMor8vUX+3iJ/73EMvEX+3iJ/b5G/t8i/9DiOo02bNmnMmDFas2aN/vrXv2rt2rW65557JEk+n0+SZGaSpKysLPfn/MyYMUPTp09X69at9fPPP2vgwIGSpMzMTGVmZqpixYoKCQnR2rVrNWXKFCUnJ0v6M/vKlSurfv36OvPMMyUpqI9NMKEwhEJp166dOnToIMdxNH36dM2ZM+eYLwpSzklLkpYsWaINGzZIkrp06XLMbTjh5I/8vUX+3iJ/73EMvEX+3iJ/b5G/t8i/ZJmZ0tPTde+992rOnDm64oor9MILL6hy5crKzs6WmQUU07KzsxUWFibHcfT7779rz549efY5ffp0paenq2fPnmrbtq18Pp/MTGFhYQoPD1d6erpbbHrnnXe0YMGCYxaagvXYBB0vuyuhbHn//fetTZs25jiOtWnTJmB8a+7xppmZme7Pe/bssd69e1tYWJg98MADpdnccof8vUX+3iJ/73EMvEX+3iJ/b5G/t8i/ZC1dutQqV65s8fHx9v333+e7ztHZ3nfffeY4jo0dO9YyMjLcZQcOHLAGDRqY4zg2a9Ysd1v/EL/t27db/fr1bdiwYXb22Web4zh233335ZlrCMGHwhAKLTU11f71r39ZlSpVzHEcu/HGG+2bb74xs/wnInv77betRo0a7rrr1q0zs2OPS0bByN9b5O8t8vcex8Bb5O8t8vcW+XuL/EvWU089ZY7jWNu2bY+b0fjx461WrVru3E2tWrWyZcuWmdmfx8I/eXWvXr3ybD9kyBBzHMdmz55tS5YssX/84x/F/4BQJlEYQpGsX7/ehg0bZo7jWEREhFWtWtVeeuklW7p0qf3xxx+2aNEie//9961Lly7uCatfv362bds2r5teLpC/t8jfW+TvPY6Bt8jfW+TvLfL3FvmXnB49erh5meVfQFu2bJk1adLEzfbqq6+2atWqmeM49o9//MOSkpLcdWfPnm0xMTHmOI7dcccdNmnSJHvrrbfcia0vv/xyS0lJCdg/E0uDwhBOSL9+/ez00093T07x8fHuDPr+29q0aWOff/65uw2XNyw+5O8t8vcW+XuPY+At8vcW+XuL/L1F/sXH5/NZenq6XXPNNeY4jl155ZWWlpaW77oJCQnmOI61a9fOFixYYGZmEyZMMMdxrGrVqjZ37lw357S0NHv88cfd4xESEuL+fNlll9maNWvytANwzI4x0xRwFDOT4zhKT0/XqlWrNGnSJM2ZM0eHDx/WwYMH1blzZ1WvXl1XXXWVbrvtNne77OxsJi4rBuTvLfL3Fvl7j2PgLfL3Fvl7i/y9Rf4lp2fPnvrqq6/Uo0cPvfnmm6pRo0a+6y1dulTt27eXlHM89u/fr8suu0yLFy/W3/72N40bN07Vq1d31584caJmzJih7OxsZWdna+DAgbr55ptL4yGhDKIwhBPif3GQpJSUFO3fv18xMTHKyMjQaaedpsjISEk5VyUICwvzsqnlEvl7i/y9Rf7e4xh4i/y9Rf7eIn9vkX/x8Wc5adIk95LyixYtUqdOneTz+QKuRpZbZmamwsPDJUkff/yxrr32WknSG2+8oVtvvVWO47jbZmVlKT09XdHR0e5tFOuQHwpDKBa5XyQkyefzyXGc417OEsWD/L1F/t4if+9xDLxF/t4if2+Rv7fI/+R9/fXXevDBB7VmzRpdf/31+vDDD/Pkeiz+Hkd/+9vfNGXKlAILShSEcCz5P2uAIjr6xBUSEsILQikif2+Rv7fI33scA2+Rv7fI31vk7y3yP3nt2rVThw4d5DiOpk+frjlz5hw3w6ysLEnSkiVLtGHDBklSly5djrkNRSEcC4UhAAAAAAA8UKNGDV155ZVq1aqVJOnBBx90iz1STq8sP39BKCwsTHv37tWIESO0YcMGDRo0SA888ECBvYWA4+GZAwAAAACAR6688kr17t1blStX1sqVK/XII49ozpw5edbzz9v0zjvvqHXr1vr000913XXX6e9//7uknKF8wIlgjiEAAAAAADy0YcMGTZo0Sc8//7zCw8MVFxenJ554Ql27dlVsbKwOHDig9evXa+zYsUpISJAk3X333Ro+fLhq167tcetR1lEYAgAAAADgFNC/f399/fXX2rx5sySpSpUqOnDggKpVq6Y9e/ZIklq3bq1nn31Wl19+uaS8k4ADRUVhCAAAAAAAD/mLO+np6Vq1apUmTZqkOXPm6PDhwzp48KA6d+6s6tWr66qrrtJtt93mbsfVxlAcKAwBAAAAAHAKyN37JyUlRfv371dMTIwyMjJ02mmnKTIyUlLORNT+OYeAk0VhCAAAAACAU9DRw8R8Pp8cx2HoGIoVhSEAAAAAAIAgxeXqAQAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIURgCAAAAAAAIUhSGAAAAAAAAghSFIQAAAAAAgCBFYQgAAAAAACBIhXndAAAAENy6deum7777LuA2Myu2/b/55pu64447Tmofb7zxhm6//fbiaVApWrp0qT7++OOA24YPH+5JWwAAwKmJwhAAAEA5tXTpUo0YMSLgNgpDAAAgN4aSAQAAAAAABCl6DAEAgHLt+uuvV7du3fLcPn36dA0dOjTgtueff17XX399nnWrVq1aUs0DAADwFD2GAABAmZGWlqaJEyeqV69eqlOnjipUqKCYmBg1bNhQN9xwgz788ENlZ2cHbBMbG6sGDRrk+Zdfsadq1ar5rhsbG1vktn755Zfq0aOH4uPjFR0drdatW+vJJ5/U4cOH9eabb8pxnIB/R1u1apXGjBmjW265RR07dlT9+vUVGxuriIgIVatWTWeffbaGDBmiNWvW5Nl2+PDhchwn37mVjr7f/IaWJSUl6cUXX1SPHj1Uq1YtRUZGKi4uTq1bt9bf//53/fbbb/k+5oYNGwbse8qUKQHL58yZE7D8zjvvDFh++PBhRUZGBqwzf/58d3liYqL+85//6I477lCXLl3UoEEDxcXFKTw8XPHx8erYsaPuvfdeLVq0KGC/KSkpqly58jHb5rd48eI8Ga1duzbfdQEAKBcMAADAQxdccIFJCviXn++//97q1KmTZ92j/3Xo0MHWrVt33Pt944038mz7xhtvFMtjeuSRRwpsX4sWLezZZ5897mN+4IEHjvtYJVlYWJiNGzcuYNsnnniiUNtKsieeeCJg2//973922mmnHXObkJAQe/LJJ/O0+c477wxYr1+/fsdsV+PGjQOWz5kzJ2B5bGysHTlyxF3+wgsvFOoxOY5jDz30UMC+Bw8eHLDOueeem++xGzp0aMB65513Xr7rAQBQXtBjCAAAnPIWLVqkHj16aNu2bcddd8mSJbrwwgu1c+fOUmhZXm+//bZGjhxZ4PI1a9boscceK7b7y8rK0kMPPaRvvvnmpPf1xRdf6Oqrr9a+ffuOuZ7P59Pjjz+up556KuD2Cy+8MOD3H3744Zi/r1+/Xjt27HB///777wOWn3feeQoPDy90+/3MTOPGjdPrr7/u3vb3v/9dISF/vvWdP39+vr2tpk2bFvD70b2aAAAobygMAQCAU5qZ6e6779bhw4fd20JCQvTII4/ol19+0ffff69bb701YJstW7boH//4R2k3VZmZmXnuNzw8XM8//7xWr16t77//Xt26dVNmZuZx9xUdHa1evXpp/Pjx+vLLL7V48WKtW7dOCQkJmjRpkurWrRuw/pgxY9yfBw8erI0bN+r555/Ps9+NGzcG/Bs8eLCknGFcd911V8BQvLPOOkszZ87UmjVr9NNPP+nuu+8O2NeIESP0+++/u78fXRj67bfftGfPHjebn3/+OU97cheDji4MHb2/iIgIXXjhhRo3bpw+//xz/fLLL1q3bp1+/fVXTZkyRa1atQpYP/fjb9y4sXr16hWwfPLkyQG/L1y4UImJie7vFStW1F//+tc8bQYAoFzxussSAAAIbscbSvb999/nWf6vf/0rz34uu+yyPEOsDh06VOD9lsRQsi+++CLPPh977LGAdVJSUqxq1aqFGj53LNOnTw/YPiYmxrKzswPWye8xnhr3EQAANeZJREFUFuTtt98OWK9atWqWmpqaZ73zzjsvYL2HH344YHnz5s0Dln/00UdmZvbTTz+5t9WsWdP9+b777jMzsyNHjlhUVFTAtgkJCUXKJCEhIc/j3blzp7t89uzZAcvi4+MtPT3dXf7QQw8FLL/77ruLdP8AAJRF9BgCAACntO+++y7Pbf379z/ubVlZWfrpp59KrF35WbhwYZ7b+vXrF/B7TEyMbr755kLtb+nSpXrooYd09tlnq3r16qpQoYI7IfLRV09LTU3VgQMHTrjtR+e8Z88excTE5JmI+ccffwxY73i9fPzDx3IPIxs8eLA74bZ/+0WLFgX0CqtSpYo6dOiQp53r16/Xo48+qq5du6pWrVqKiopy29a5c+c862/dutX9+eKLL1bLli3d3/fv368ZM2ZIyumZxjAyAEAwojAEAABOadu3bw/4PSIiIs8wKklq1KjRcbctaUfPa1RQWxs3bnzcfT322GPq2LGjXnjhBS1cuFB79uxRRkbGMbdJSUkpWoNzKcz8TfnJPUeQVHBhKHcB6aqrrlLr1q0lSStXrtSBAwfyFJguuOCCgDmBpJyhX82bN9fIkSP1448/aufOnUpPTz9m+47OZNCgQXn2KUk//fSTtmzZ4t7eokULnXPOOcfcNwAA5QGFIQAAgBJS0KXoj+frr7/W008/LTMr0nZFXb845O7lI0ndu3cPeMxLlixRUlKSe9n5qlWrqnnz5jr//PMl5bT5xx9/PG7Po9WrV+vee+9VVlZWkdp3dCZ9+vRRlSpV3N+/++47/f777/rggw8C1rvrrruKdD8AAJRVFIYAAMAprXbt2gG/HzlyJGB4kN+GDRvy3FarVq0Sa1d+atasGfB7RkZGnh41Us5wqGN57733An6vUKGCRo8erYSEBG3YsEEbN27Um2++edLtze3onFu0aJFnour8/i1ZsiRgu/j4eLVr1879PTs7W6+++qo7zO28886T4zjq2rWru868efPcwpHf0YWhadOmBUyMHRISon/+85/6+eef9ccff2jjxo2FujJbdHR0nqLPpEmTNH36dPf38PBw9enT57j7AgCgPKAwBAAATmkXXHBBntsmTZp03NvCwsL0l7/8pcTalZ+zzjorz21vvfVWwO+pqal5Cj9HO3pY16WXXqphw4apU6dOatiwoRo0aKBFixYdtz0RERF5bju6h49ft27dAn7/7bfftH37djVo0CDff6effrrWrVuX7xXWji7qjBs3zv3ZXxDy9xiSpDfffFNJSUnu7zVq1MhzhbGjM2ndurVGjhyps846S40bN1aDBg3yFKkK8ve//12hoaHu7y+99FLAsMMrrrhC1atXL9S+AAAo6ygMAQCAU9p5552nNm3aBNw2atQoPfroo0pISNCPP/6oPn366KuvvgpY55ZbblFcXFxpNlUXXXRRnp43jz/+uEaPHq1ly5bphx9+0BVXXKG9e/cecz/VqlUL+H3u3Ll666239Ntvv2n+/PkaMGCAxo8ff9z2HL0fSRo9erR+++03JSYmKjEx0R2ade211wb0eDIz9erVS08++aTmz5+vdevWacmSJfrwww/14IMPqlGjRrr00ku1efPmPPdxdGEod9HFXxiqVauWO9fS/v37A9bv3r37cR/L6tWr9eKLL2r16tX65Zdf9Mgjj+iRRx45Zh5+p59+uq666ir396OLWwwjAwAEE8e8GIwOAADw/3Xr1i3PFbGOfnvyyy+/qFu3bgX2djlavXr19Msvv+QZ2pXbm2++qTvuuCPgtjfeeEO333574RpegHfeeee4w5CioqLyPJbcj3nGjBl5rjp2tFq1auUZprZx40Y1aNDA/X3//v2qWbNmvr168tvms88+09VXXx0wZOt4vv322zy9jZKTkxUfH59nPqCYmBgdPHhQYWFhknKu+vXGG2/k2efkyZPzXM0tISFBXbp0OWZb8sskv/ZJOcPX8itA1a5dW5s3bw7oUQQAQHlGjyEAAHDKO/PMM/Xll1/m6Y2Tn/bt22vu3LnHLAqVpFtvvVX//Oc/C1zevn17jRgxIuC2o4d8XXvttbrpppsK3Ee7du0K1WMoPj5eAwcOPO56fldccYU+/vhjVa1atVDrV6xYUZUrV8739vwuHX/OOee4RSFJAfMM5XZ0jyNJ6ty5s/7v//6vwLbUr19fU6dOLUSrc3Tr1k1t27bNc3vfvn0pCgEAggqFIQAAUCacf/75WrdunSZMmKCePXuqVq1aioiIUFRUlOrXr6/rrrtOH3zwgRISEtSkSRNP2zpy5Eh98cUXuuSSS1SpUiVFRUWpdevWevrpp/Xzzz+7EzH7HT2fjeM4evfddzV58mR16dJF0dHRiomJUevWrfXUU0/p559/Driy1rG88MIL+s9//qMuXbooNjb2uOtfccUV2rBhg15++WX16tVLderUUYUKFRQeHq5q1arp7LPP1n333aePPvpIu3btUvv27fPdT37FnaMLQbnnGfKrX7++O8TsaM8++6ymTZum888/XxUrVlSFChV0xhlnaOjQoVq6dKkaNmx43MeX29GXrpdyejEBABBMGEoGAABQijIyMtSxY0etXr3ave2GG27Qhx9+6GGrgtMPP/wQUJw6//zz8wxrBACgvKPHEAAAQDG75JJL9Morr2jTpk0Bt69atUo333xzQFFIopeKFw4dOqTHHnss4LaiDLsDAKC8oMcQAABAMatcubIOHTokSYqOjlZcXJySk5OVmpqaZ90+ffpoypQppd3EoNWkSRNlZWVpx44dOnLkiHt7y5YttXz5cuYXAgAEnbDjrwIAAIATlZaWprS0tDy3h4SE6IEHHtBzzz3nQauC1/r16/PcFhsbqylTplAUAgAEJXoMAQAAFLPPPvtM33zzjX7++Wdt375de/bskZmpcuXKat68ubp27aq+fft6Pkl2MHIcR5IUGhqqunXr6oILLtC//vUvnXHGGR63DAAAb1AYAgAAAAAACFJMPg0AAAAAABCkmGMIx2VmyszMlM/n87opAAAAAACUC6GhoQoPD/e6GRSGULAjR45o9+7dSktLU3Z2ttfNAQAAAACgXImMjFTVqlUVFxfnWRsoDCFfaWlp2rJli0JDQ1WlShVFRUUpNDTUnbARAAAAAACcGP/InEOHDmnbtm2S5FlxiMmnka/NmzcrKytLp59+OpduBQAAAACgBJiZtm7dqszMTDVq1MiTNjD5NPLIyspSamqq4uPjKQoBAAAAAFBCHMdRpUqVlJGRoczMTE/aQGEIeWRlZUnKGesIAAAAAABKjn8Caq/m9qUwhAIxnxAAAAAAACXL68/eFIYAAAAAAACCFIUhAAAAAACAIEVhCAAAAAAAIEhRGAIAAAAAAAhSFIYAAADKmcTERDmOI8dxlJiY6HVzAADAKYzCEAAAwFGGDx/uFlb8/0JCQhQXF6e6devqL3/5i+677z5Nnz5dR44c8bq5AAAAJyzM6wYAAACcymrUqOH+fPjwYW3fvl3btm3TTz/9pAkTJui0007T008/rQEDBnjYykDh4eFq1qyZ+zMAAEBBKAwBAAAcw86dOwN+z87O1urVqzV79my9/PLL2rhxowYOHKgffvhB77zzjhzH8ailf6pTp45+++03r5sBAADKAIaSAQAAFEFoaKjatGmjhx56SCtXrtRNN90kSXr33Xf17LPPetw6AACAoqEwBAAAcIKio6P11ltvqUOHDpKkZ599Vvv378+z3pEjRzRhwgR1795dVatWVUREhGrWrKnevXvriy++yLP+zJkz5TiOIiIitG/fvmO24fzzz5fjOLrrrrvc2woz+bTP59OHH36oq6++WnXq1FFkZKSqVaumTp066f/+7/+0cuXKfLdLTk7Ws88+q3POOUfx8fGKjIxUvXr1dNNNN+mnn34qsJ0HDhzQ448/ro4dOyouLs7NoG3bthowYIDmzJlzzMcJAABKBkPJAAAATkJERIQeeeQR3XDDDUpKStLHH3+sO++8012+adMm9erVS6tWrZIkOY6juLg47dq1S59++qk+/fRTDRgwQBMnTnS36dWrl+Lj47V///7/196dhzV1rmsDv0MgERFBQESkEhTUHidQ68RRRKEqapn21larolCLdbfHqbZ6Tqvus3W3l6J1d1APirVu0Gq1Vip4yiBaWpFdRToIlYqIiqABGZQxZH1/+CXHyJRAMEDu33XlarPWu971JPoIefIOOHLkCFasWNHovfPy8pCamgoAWLRokdYxy+VyBAcH4/z58+pj1tbWqK6uxuXLl3H58mX8/vvvOHnypMZ1V65cwZw5c3D79m0Aj0dPde/eHbdv38aXX36Jo0ePYsuWLVi/fr3Gdbdv34anpyfy8/MBACYmJrCysoJcLkdRURF++eUXZGdnY9q0aVq/BiIiItIPjhgiIiIiaqMZM2ZALBYDAM6dO6c+/ujRI8yYMQO//fYbpkyZgpSUFFRVVaG0tBSlpaXYsWMHevTogT179mDXrl3q6yQSCebNmwcAOHToUJP3/ec//wlBECCTyTB58mStYlUoFAgICMD58+chlUrx4Ycf4t69e3jw4AEqKipw584d7N27F//2b/+mcd3du3cxffp03L59G0FBQfjpp59QVVWF8vJyFBUV4b333oNYLMaGDRsaFJQ2bdqE/Px8yGQyJCYmora2FiUlJaipqUFeXh52796N8ePHaxU/ERER6RdHDBERERG1UY8ePTBgwADk5OTg+vXr6uM7duxAdnY2vLy88N1332nsEGZlZYVVq1ZBJpMhKCgIf/vb37BixQqYmj7+9WzRokXYvXs3Ll68iGvXrmHQoEEN7qsqGr366qtaL3p98OBB/PDDDxCJRDhx4gT8/Pw0zjs6OmLZsmUNrvuv//ov3Lt3D/Pnz0d0dLTGOXt7e/z1r39Fr169sHr1amzatAkBAQHq8z/++CMAYOvWrRqjgsRiMZydnTvUjm5ERETGhiOGiIiIiPTAxsYGADTWGNq/fz8AYPXq1U1uGx8QEICePXtCLpfj0qVL6uPjx4+Hm5sbgMZHDaWnp+PatWsAgIULF2odZ1RUFADAz8+vQVGoKdXV1YiJiQEAvPPOO022U01ny8zMRFFRkfq4tbU1gMejjoiIiKhjYWGIiIiIqB3cuXMHN2/eBACEhobCwcGh0Uffvn3x8OFDAFC3V1EVfFRTxp6kKhaNGzeu0dFEjVEoFPjXv/4FAJgzZ47Wr+XSpUuorq4GALz44otNvpahQ4eqr3nytcyePRsA8O6772LZsmU4c+YMysvLtb4/ERERtR9OJSMiIiLSA9VIIVtbWwBAQUGB+pxcLteqj8rKSo3nCxcuxMaNG9WLTE+aNAkAUFdXhyNHjgDQbdHp4uJi1NXVAQCcnZ21vu7J1/LkSKDmPPla3n77bWRmZuLo0aOIjIxEZGQkRCIRhg4dihkzZiAsLAyDBw/WOh4iIiLSH44YIiIiImqjhw8fIjc3FwAwcOBAAEB9fb36fFZWFgRBaPEREhKi0a9MJlMXg7744gv18TNnzkAul2ssUq0NbdchetqTr6Wqqkqr1zJlyhT1NWZmZvjyyy9x5coVvP/++5g6dSq6d++OX3/9Fdu3b8fQoUMRERHRqtiIiIiobVgYIiIiImqjM2fOqIsnqoKIg4OD+vzTU8R0oZpOduzYMfV0LtU0Mj8/P/UIJW3Y2Nio1zrSJSZ9vZaRI0di8+bNSEpKQmlpKRITEzF58mTU19erRxURERHRs8XCEBEREVEb1NbWYuvWrQAe7zSm2o1LJpOhX79+AIDY2NhW9//nP/8Z3bp1Q1lZGWJjY9X/BXSbRgYApqamGDt2rM4xvfDCC5BIJDpf11Is06ZNw+nTpyGVSiEIAhITE/XSNxEREWmPhSEiIiKiVqqqqkJISAgyMjIAAOvXr1fvwAUAr732GoDHu5Op2jTlyd3MnmRlZQV/f38Aj6eTqUYO2djYYNasWTrHHBoaCgCIi4tDXFycVtdYWFhg/vz5AIAPP/wQ+fn5zbZ/+rXU1NQ02VYqlUIsFgMATEz4qykREdGzxp++RERERDpQKpX49ddfsWPHDgwdOhSHDx8G8HjK17p16zTarlmzBsOHD0d1dTW8vb3xySefoLi4WH2+tLQU8fHxWLRokXotocaoppOdOXMGn3zyCQBg3rx56lE8uli4cCH+/d//HYIgIDg4GNu2bdNYHLugoAA7d+5ssC391q1b4ejoCLlcjgkTJuDQoUOoqKhQn79//z6OHz+OwMBAvPLKKxrXOjs7Y/369UhLS9MoEv3xxx9YsGABKisrYWJigunTp+v8eoiIiKhtRMLTe5+S0auursaNGzfg4uKCbt26GTocIiKiZ27Tpk3YvHkzAKBPnz7q4zU1NSgvL4dSqVQfs7Ozw9/+9je8/vrrjfZVUFCA4OBgpKWlAXi8ALSVlRWUSqXGlu2urq7IyclptA+FQgEnJyeNHcEuXLiA8ePHN9o+Ly8PLi4uAIAbN25AJpNpnJfL5QgKCsL333+vEZNCocDDhw8BAP7+/jh58qTGdVlZWQgICMC1a9cAPB7hY21tjZqaGjx69EjdzsfHBwkJCernTy56bWJiAisrK1RVVanXTBKJRNixYwdWrlzZ6OshIiLqygz9GZzb1RMRERE1Q1WMEYlEsLCwgIODA/r37w8PDw9MmzYNc+bMaXbkjqOjI1JTU3Hs2DEcPnwYP/30E+RyOUxMTCCTyTB8+HBMmzYNc+fObbIPU1NTvPLKK/joo48AAG5ubk0WhbRhZ2eHlJQUHD58GNHR0bh06RIePHiAXr16YfDgwfD19VWPUnrS888/j59//hkHDx7E8ePHceXKFZSUlEAikcDV1RUeHh7w9fXFn/70J43rvvvuO5w9exapqanIz89Xv6eurq6YNGkSVqxYgdGjR7f69RAREVHrccQQNWDoaiURERERERGRsTD0Z3CuMUREREREREREZKRYGCIiIiIiIiIiMlIsDBERERERERERGSkWhoiIiIiIiIiIjBQLQ0RERERERERERoqFISIiIiIiIiIiI8XCEBERERERERGRkWJhiIiIiIiIiIjISLEwRERERERERERkpFgYIiIiIiIiIiIyUiwMEREREREREREZKRaGiIiIiIiIiIiMFAtDRERERERERERGioUhIiIiIiIiIiIjxcIQtVq9UjB0CDrpbPFS16IUlIYOQSedLV7qWpTKzvX3r7PFS12LIHSu3286W7zUtSg72d+/zhYvdV6mhg6AOi+xiQj/cSQDf9x7aOhQWuRq3wO7XvbQa58ymQw3b95UPxeJRLCwsICVlRXc3NwwevRozJ07F2PHjtXrfVtLJBIB6Ni/kCUmJmLHjh1IT0/Ho0eP4OzsjODgYKxfvx49evQwdHhtYiIyQdL9b1FaV2zoUFpkbWaLab1n67VP5ot+KJVKpKWl4cyZM0hOTkZWVhbKy8thZWUFDw8PhISEYP78+er4OysTExOc/ng7Su7cMnQoLbLp9xxmvblWr30yX9rXZ599hhUrVgAAQkNDsW/fPgNH1DYikQg5OTmoqqoydCgtMjc3h5ubm177ZL7on1KpxKFDh3Do0CFkZmaivLwcNjY2eP755/GnP/0Jb7zxhqFDbDUTkQjnCh+grFZh6FBaZCUxhZdDL732yXzRr+LiYmzfvh3ffvstcnNzUVdXB3t7e0yYMAFvvvkmJk+ebOgQtcbCELXJH/ce4reCckOHYVCenp5wdXUFAFRVVUEulyMjIwMpKSmIiIiAl5cXoqKiMGDAAANH2rHt3LkTq1evhkgkwqRJk9CnTx98//332Lp1K44fP47U1FTY2dkZOsw2Ka0rhrz2nqHDMCjmS9vk5ubC09MTAGBjY4MxY8agV69eyM3NRWJiIhITE3HkyBEcP34cEonEwNG2TcmdW7h347qhwzAo5ov+5ebmYt26dRCJRB32g0ZrVFVVobKy0tBhGBTzRT/Kysrw0ksv4fz58+jZsycmTpwIa2tr3LlzBxkZGSgvL+/UhSEAKKtVoLimztBhGBTzpe2uX7+OyZMno6CgALa2tpgyZQq6d++O3377DV999RW++uorREREYPXq1YYOVSssDBG1UVhYGEJCQjSOCYKA+Ph4rFy5EufOncPEiRNx4cIFuLi4GCZIAFlZWQa7d0syMjKwZs0aiMVixMbGYubMmQCAyspKvPTSS0hKSkJ4eDi++uorA0dKbcV8aRuRSISpU6fi7bffhq+vL8RisfrcuXPnMGvWLHz77bf44IMP8P777xswUtIH5ot+KZVKhISEQCQSYdGiRTh48KChQyI9Yr60nSAICAgIwPnz5/H6669j+/btGiO2a2tr8fPPPxswQtIX5kvbrV69GgUFBZg1axa+/PJLWFhYqM/9z//8D15//XW88847mDt3LpycnAwYqXa4xhBROxCJRPDz80N6ejrc3NxQVFSEsLAwg8Y0ZMgQDBkyxKAxNOXvf/87BEHAkiVL1EUhAOjevTv2798PExMTHD9+HNnZ2QaMktoL80V7AwcORFJSEmbMmKFRFAIALy8vvPvuuwCAL774whDh0TPAfGm9Xbt24fvvv8eHH34ImUxm6HDoGWC+6ObAgQNISUnB9OnTsWfPngbT+CUSCcaMGWOg6Ki9MV90k5ycDADYuHGjRlEIAJYtWwY3NzcoFAr861//MkR4OmNhiKgdWVtb46OPPgLw+B+PS5cuNWijUCiwb98+TJkyBTY2NpBKpXBxccHy5ctx65bm+hp79+6FSCTCjBkzmrxncXExpFIpJBIJ7t+/rz4uEomaXHdEoVAgKioKPj4+sLOzg1QqhZOTE3x8fPDxxx83ek1SUhKCgoLQt29fSCQS2NvbIzAwEBcuXGjpbdFQW1uL06dPAwDmz5/f4Lyzs7N66szXX3+tU9/UuTBf2s7D4/Faak+/F9T1MF908/vvv+M///M/4eXlheXLl7e6H+qcmC/a+cc//gEAePvtt3W+lroO5ot2unXrplW7zrIUBgtDRO1s5syZsLGxAQAkJCRonKuoqICvry9ee+01XLp0CSNGjMBLL70EqVSKPXv2wMPDAxkZGer2L7/8MszNzZGQkIA7d+40er+YmBjU1tZizpw56N27d4vxlZWVwdvbG6GhoTh//jyGDRuG4OBgDBo0CD///DPeeuutBtesXbsWPj4++Oabb9C/f38EBARgwIAB+OabbzBp0iQcOHBA6/fn2rVr6nURmvoWSnX8yfeCuibmS9vk5OQAAPr27au3PqnjYr5op76+HosXL4ZIJML+/fs7/eLs1DrMl+YVFRUhMzMTYrEYEydORG5uLj744AOEh4dj7dq1OHbsGGpra7Xujzo35kvLVLMcNm/e3GCNt8jISOTk5GD48OGYMGGCTv0aCgtDRO1MJBJh1KhRAIDffvtN41x4eDhSUlIwe/ZsXL9+HSkpKTh27Biys7Oxc+dOFBcXY968eaivrwcAWFlZISgoSL1bRGNU/6gtWbJEq/iWLl2K1NRUeHh44Nq1a0hJSUFMTAySk5NRUFCAkydParSPjIxEREQEXF1dkZGRgYsXL+Lo0aNIS0tDSkoKunfvjvDwcPUH1JbcuHEDwONvJywtLRtt89xzz2m0pa6L+dJ6lZWV6m97g4OD29wfdXzMF+1s27YNFy9exJYtWzBw4ECdrqWug/nSPNXaQba2tti3bx+GDBmC9evXY+/evYiIiMDcuXPx/PPPc40hI8F8adm2bdswevRonD59Gv3798fs2bMxd+5cDB06FOHh4Zg1axb+93//F6amnWNZZxaGiJ4B1RDC4uL/26o8KysLhw8fhqOjI2JiYmBvb69xzcqVK+Hn54ecnBzEx8erjy9duhQA8Pnnnze4T2ZmJjIyMuDg4NDscM0n2584cQLdunVDbGxsgzUXTE1N4e/vr36uVCqxadMmAMCRI0cwYsQIjfaTJ0/Ge++9h9raWuzdu7fF+wOPv3UA0GBu7pNUc9zLy417BzxjwXxpnTfeeAM3btyAo6MjNmzY0Ob+qHNgvjTv119/xcaNGzFx4sRGv0Em48J8aZrqPSkpKcFbb70Ff39//PLLL6ioqMCFCxcwbtw45ObmYsaMGRrvH3VdzJfm9enTBykpKXj11VdRXFyM06dP49ixY7h69Sr69euHqVOnajX6qaNgYYjoGVAqlQCgMXw9Li4OgiBg5syZTY6UmTJlCgDgxx9/VB/z9vaGTCbD77//3mA+rKravmjRIq2q02fOnAEAzJo1C/369WuxfUZGBgoKCjBw4ECMHj1a65iJdMF80d1///d/4+DBg+jWrRuOHj0KW1vbNvVHnQfzpWkKhQKLFy+GiYkJoqKiYGLCX3uNHfOlaYIgAHicNxMmTMCxY8cwbNgw9OjRA+PHj0dCQgL69OmDu3fv4rPPPtOqT+rcmC/Ny87OhoeHB2JjY/HZZ5/h1q1bKCsrQ0pKCvr06YM1a9bAz89PPXKqo+NPSKJnQC6XA4B6ri4A5ObmAoB6vYPGHuvWrQOABouwqbaXfHIubF1dHaKjowFoPwzz5s2bAKD1av+qmK9fv95kzGPHjm0Qc3NUP1QePXrUZJuHDx8CAHr27KlVn9S5MV90s2PHDrz//vuQSqX4+uuv1Yu1k3FgvjRty5YtuHz5MjZv3ozBgwdrdQ11bcyXpj35If/1119v9Pyrr74KAEhMTNSqT+rcmC9NUygUCA4Oxh9//IHIyEgsX74cTk5O6NmzJ7y8vPDdd9/BwcEBCQkJnWan2M4x4Y2oExMEQb0A2/Dhw9XHVVV4d3d3jBw5stk+xo0bp/E8JCQEmzdvxtGjR7Fr1y6Ym5sjNjYWcrkc48ePb7dtHVUxOzg4YPr06c221XYFftXwz9LSUlRUVDT67YNqdwNuL9z1MV908/HHH2PNmjWQSCQ4fvy4VkOwqetgvjRPtZNlbGws4uLiNM7l5eUBAE6fPq3+pjglJUX7gKnTYb40b8CAAY3+f2Nt7t69q1Wf1HkxX5p38eJFXL16FVKpFEFBQQ3O9+rVCzNnzsSBAweQmJioddHLkFgYImpncXFxePDgAQDgxRdfVB9XLajs6emJTz75RKc+nZ2dMXXqVCQlJeHEiRNYsGCBes6uag6vNvr37w/g8VBIbahitrW1bXSOcGsMHjwY3bt3R2VlJX766Sd4e3s3aPPTTz8BgHoRPOq6mC/a+/TTT/HWW2+pi0KzZs3S+z2oY2O+aCc1NbXJc4WFhSgsLNTr/ahjYr40b9CgQbC0tERFRYV6pMjTVMdVaz9S18V8aV5+fj4AoHv37hCLxY22sbKyAvB43a7OgFPJiNpRWVkZVq1aBQDw9fWFu7u7+pxqi8NTp06hurpa576fXMStqKgI8fHxMDc3x7x587TuQzW6IC4uDgUFBS22f+GFF2BnZ4erV6822KGgtSQSifoDbUxMTIPzN2/eVM/3DQwM1Ms9qWNivmhvz549+Mtf/qIuCs2ePVuv/VPHx3xp2ZUrVyAIQqOPjRs3AgBCQ0PVx6jrYr60zNTUFAEBAQCaniqm2rZcNe2GuibmS8tUaxs9ePCgyZ3MLl68CABwcXHRyz3bGwtDRO1AEATEx8dj7NixyMnJQd++fREZGanRxsPDA8HBwbh16xaCgoLUw9qf9OjRI0RHR6OoqKjBuaCgIFhbWyM5ORlbtmxRz3XVZR0ed3d3+Pv7o6qqCv7+/urqt4pCocCpU6fUz83MzLBx40YIgoDAwMBGv4Wtr69HcnIy0tLStI7j3XffhUgkwoEDB9QLygGPt98ODQ1FfX09goOD222IKRkW80W3fImMjMQbb7zBopCRYr7oli9k3JgvuuXLhg0bYGZmhsjISHz77bca57Zt24bU1FSIxWKsWLFC6z6p82C+aJ8vEyZMUBeHwsLCNNYmUiqV+OCDD9SLbL/yyitavzZDEgn8ioSeUl1djRs3bsDFxQXdunVrtu1/HMnAH/cePqPIWs/Vvgd2veyh1z5lMhlu3rwJT09PuLq6AgBqamogl8tx+fJl9bDBKVOmICoqqtFqcUVFBQIDA5GUlASJRIKRI0fCxcUFgiAgLy8PmZmZqK2tRVZWVqNFkeXLl2PPnj3q58nJyY1OxQL+b0eBp1P+wYMH8PPzQ1paGiQSCSZOnAhHR0cUFhbil19+wf379xtcs27dOmzbtg0AMHToULi6usLc3ByFhYW4cuUKSktLsXv3boSHh2v7dmLnzp1YvXo1RCIRvLy8YG9vj++//x53797F4MGDkZqa2up1WDqKpPvforSu42/xam1mi2m99VtsYL7oJ1+uXLmCUaNGQRAEDBkypMH8/Se1x/S1Z+n0x9tRcueWocNokU2/5zDrzbV67ZP5ot+fL43ZtGkTNm/ejNDQUOzbt69NfXUEOTk5qKqqMnQYLTI3N4ebm5te+2S+6DdfDh48iKVLl0KpVGLMmDGQyWT49ddfkZ2dDbFYjN27d+O1117Tur+O6FzhA5TVKgwdRousJKbwcuil1z6ZL/rLl+TkZMyZMweVlZXo2bMnxo0bB0tLS2RmZuL69esAHhdbt2zZolV/unwGbxcC0VOqqqqEq1evClVVVc22U9Qrn1FE+qHveJ2dnQUAGg8LCwvB0dFR8PLyEtasWSOkp6e32E99fb0QExMj+Pn5CX369BHMzMwEW1tbYdiwYcKSJUuEr7/+WqitrW302vT0dPW9ZTKZoFQ2/RpV7RpTU1Mj7N69W5g0aZJgbW0tSCQSwcnJSfD19RU+/fTTRq/54YcfhAULFgjOzs6CVCoVLC0thUGDBgkBAQHCvn37hJKSkhZf+9MSEhKEGTNmCDY2NoJUKhXc3NyE9evXC+Xl5Tr31dHUK+sNHYJO9B0v80U/+XL27NkG72NTj86svr6T5Yue42W+6P/ny9M2btwoABBCQ0Pb3JehNfdn0xHpO17mi/7zJT09XQgODhbs7e0FMzMzwcHBQfjzn/8sXLx4Uee+Opr6TpYv+o6X+aLffLl+/bqwYsUKYciQIYK5ublgZmYmODo6CoGBgcJ3332nU1/afgZvLxwxRA0YvFpJREREREREZCQM/RmcawwRERERERERERkpFoaIiIiIiIiIiIwUC0NEREREREREREaKhSEiIiIiIiIiIiPFwhARERERERERkZFiYYiIiIiIiIiIyEixMEREREREREREZKRYGCIiIiIiIiIiMlIsDBERERERERERGSkWhoiIiIiIiIiIjBQLQ0RERERERERERoqFISIiIiIiIiIiI8XCEBERERERERGRkWJhiIiIiIiIiIjISLEwRK2nrDd0BLrRc7wymQwikUj9MDExgaWlJZycnODt7Y21a9ciPT1dr/dsC1WcHdGtW7ewd+9eLFu2DKNHj4ZUKoVIJEJYWJihQ9MfpdLQEehGz/EyX/QnPj4eYWFhGDNmDPr27QupVApLS0u4u7tjw4YNkMvlhg6xzQSlYOgQdKLveJkv+rNp0yaN97KxR3Z2tqHDbBNB6Fw/X/QdL/NFf1rKFdXjiy++MHSoRKRnpoYOgDoxEzFwPAyQXzN0JC2zGwQE72uXrj09PeHq6goAqKqqglwuR0ZGBlJSUhAREQEvLy9ERUVhwIAB7XL/ruD48eNYtWqVocNoXyYmQFIiUPrA0JG0zLoXMM2nXbpmvrRddHQ0oqOj4erqimHDhqF3794oLi5Geno6/v73v2P//v1ITk7G0KFDDR1qq4lMRCg+kg3FvUpDh9IiU/vusH15SLv0zXzRn5EjR8Ld3b3Rc1ZWVs82GD0TiUxQWBiHuroSQ4fSIjMzGzg4+LVL38yXtlu8eHGT5/Lz83H27FmIRCJ4eXk9w6iI6FlgYYjaRn4NuJtp6CgMKiwsDCEhIRrHBEFAfHw8Vq5ciXPnzmHixIm4cOECXFxcDBMkgKysLIPduyUuLi548803MWrUKIwaNQpHjx7Fli1bDB2W/pU+ALrAaI62YL603dq1a7F9+3Y4ODhoHH/48CGWLl2KY8eOISwsDBcuXDBQhPqhuFeJuoJHhg7DoJgv+hMQEIBNmzYZOox2U1dXgpqae4YOw6CYL233+eefN3nujTfewNmzZ+Hj4wNnZ+dnFxQRPROcSkbUDkQiEfz8/JCeng43NzcUFRUZfFrUkCFDMGRI+3yr3Vb+/v74xz/+gZCQEIwYMQKmpqxZGxPmi27c3d0bFIUAoEePHoiIiAAApKWloby8/FmHRs8A84VIe8wX/aiursbhw4cBAKGhoQaOhojaAwtDRO3I2toaH330EQAgOTkZly5datBGoVBg3759mDJlCmxsbCCVSuHi4oLly5fj1q1bGm337t0LkUiEGTNmNHnP4uJiSKVSSCQS3L9/X328uTntCoUCUVFR8PHxgZ2dHaRSKZycnODj44OPP/640WuSkpIQFBSEvn37QiKRwN7eHoGBgZ1+lAIZDvOl7VRFVRMTE5iZmem1b+pYmC9E2mO+tM3x48dRWloKGxsbBAQE6K1fIuo4WBgiamczZ86EjY0NACAhIUHjXEVFBXx9ffHaa6/h0qVLGDFiBF566SVIpVLs2bMHHh4eyMjIULd/+eWXYW5ujoSEBNy5c6fR+8XExKC2thZz5sxB7969W4yvrKwM3t7eCA0Nxfnz5zFs2DAEBwdj0KBB+Pnnn/HWW281uGbt2rXw8fHBN998g/79+yMgIAADBgzAN998g0mTJuHAgQO6vEVEasyX1qupqcGGDRsAAL6+vjA3N9dLv9RxMV+0d/nyZbz77rtYtmwZ3n77bcTExKCioqJVfVHnxHxpvaioKADAq6++CqlUqpc+iahjYWGIqJ2JRCKMGjUKAPDbb79pnAsPD0dKSgpmz56N69evIyUlBceOHUN2djZ27tyJ4uJizJs3D/X1j3dUs7KyQlBQEJRKJQ4dOtTo/VS/BCxZskSr+JYuXYrU1FR4eHjg2rVrSElJQUxMDJKTk1FQUICTJ09qtI+MjERERARcXV2RkZGBixcv4ujRo0hLS0NKSgq6d++O8PBw5OTk6PI2EQFgvuji8uXLCAkJweLFi+Hn5wcnJyd8/vnneOGFF7B//36d+6POh/mivdjYWHz44YeIjIzE9u3bsWDBAjz33HPcXcmIMF9aJy8vD2fPngXAaWREXRkLQ0TPgJ2dHYDHw4pVsrKycPjwYTg6OiImJgb29vYa16xcuRJ+fn7IyclBfHy8+vjSpUsBNL5AYGZmJjIyMuDg4NDs8OYn2584cQLdunVDbGwsZDKZxnlTU1P4+/urnyuVSvXinUeOHMGIESM02k+ePBnvvfceamtrsXfv3hbvT9QY5ot28vPzcfDgQXzxxReIj4+HXC6Hj48Pjhw5gn79+uncH3VOzJfmDRw4EFu3bkVGRgZKSkpQUlKC1NRUzJ49G2VlZVi8eDGio6O17o86N+aL7g4cOABBEDBmzJgG9yGiroOFIaJnQKlUAoDGnPK4uDgIgoCZM2fC0tKy0eumTJkCAPjxxx/Vx7y9vSGTyfD77783mD+u+nZq0aJFWi3gfObMGQDArFmztPogmZGRgYKCAgwcOBCjR4/WOmYiXTBftBMQEABBEKBQKJCXl4d9+/YhKysLw4YNw1dffaVzf9Q5MV+at3DhQqxfvx7u7u7o1asXevXqBU9PT8TGxuLNN98EAKxatQq1tbVa90mdF/NFN0qlUl34UhXCiKhrYmGI6BmQ//8tylVz2wEgNzcXALB//371QoRPP9atWwcADRYtVG3H+uTc8bq6OvW3ntoOW7558yYAaL07hirm69evNxnz2LFjG8RMpAvmi27EYjGcnZ0RGhqK1NRUiEQiLFmyBIWFha3ukzoP5kvrbdq0CWKxGPfv38fFixf10id1bMwX3SQmJiI/Px/m5uaYP39+q/shoo6Pe0ITtTNBENQLFg4fPlx9XPWtlbu7O0aOHNlsH+PGjdN4HhISgs2bN+Po0aPYtWsXzM3NERsbC7lcjvHjx7fbNqiqmB0cHDB9+vRm26qGaxPpgvnSNjKZDN7e3jh9+jQSEhKwcOFCvfRLHRPzpW1sbGxgb2+Pu3fv4vbt23rpkzou5ovuVItOBwcHw8rKqtX9EFHHx8IQUTuLi4vDgwcPAAAvvvii+vhzzz0HAPD09MQnn3yiU5/Ozs6YOnUqkpKScOLECSxYsKBVQ3379+8PAMjOztaqvSpmW1vbRufUE7UV86XtLCwsAAD37t17Zvckw2C+tE19fT3KysoAoMkpRNR1MF90U1JSol7wmotOE3V9nEpG1I7KysqwatUqAI+3j3Z3d1efmzlzJgDg1KlTqK6u1rnvJxc9LCoqQnx8PMzNzTFv3jyt+1AtiBgXF4eCgoIW27/wwguws7PD1atXG+zoQdRWzJe2q6mpQWpqKgBg0KBBz+SeZBjMl7Y7deoUKisrIRKJMGbMmGdyTzIM5ovuoqOjUVNTg4EDB8LLy6td7kFEHQcLQ0TtQBAExMfHY+zYscjJyUHfvn0RGRmp0cbDwwPBwcG4desWgoKCkJeX16CfR48eITo6GkVFRQ3OBQUFwdraGsnJydiyZQsUCgWCg4PRs2dPreN0d3eHv78/qqqq4O/vj/z8fI3zCoUCp06dUj83MzPDxo0bIQgCAgMD1R9An1RfX4/k5GSkpaVpHQcZN+aL9vly79497N69G+Xl5Q3O3blzBwsXLkRBQQFkMhl8fX21fm3UeTBftM+X/Px8/POf/2z0w/7JkycRFhYGAFiwYAEcHBy0fm3UeTBfWv/7mGoa2dKlSzUW6yairkkkCIJg6CCoY6mursaNGzfg4uKCbt26Nd/4eBggv/ZsAmsLu0FA8D69dimTyXDz5k14enrC1dUVwONv6+VyOS5fvoySkhIAj3eFiIqKgouLS4M+KioqEBgYiKSkJEgkEowcORIuLi4QBAF5eXnIzMxEbW0tsrKyGp2nvnz5cuzZs0f9PDk5Gd7e3o3Gq/qh/nTKP3jwAH5+fkhLS4NEIsHEiRPh6OiIwsJC/PLLL7h//36Da9atW4dt27YBAIYOHQpXV1eYm5ujsLAQV65cQWlpKXbv3o3w8HCt3su7d+8iMDBQ/fz27du4c+cOevfujQEDBqiPf/bZZxg1apRWfXZISYlA6QNDR9Ey617ANB+9dsl80U++5OXlwcXFBRKJBO7u7pDJZBAEAbdu3cLly5dRW1sLR0dHxMXFtbhWRkdXfCQbinuVhg6jRab23WH7sn7XEWG+6Cdfrly5Ag8PD/To0QMeHh7o168fqqqqcPXqVeTk5AB4vLPUqVOn0KNHjxb768gKC+NQV1di6DBaZGZmAwcHP732yXzR3+9jKhkZGRg1ahTEYjHy8/Ph6Oio0/VEpDudPoO3B4HoKVVVVcLVq1eFqqqq5hvWK55NQPqi53idnZ0FABoPCwsLwdHRUfDy8hLWrFkjpKentxxWfb0QExMj+Pn5CX369BHMzMwEW1tbYdiwYcKSJUuEr7/+WqitrW302vT0dPW9ZTKZoFQqm7yPql1jampqhN27dwuTJk0SrK2tBYlEIjg5OQm+vr7Cp59+2ug1P/zwg7BgwQLB2dlZkEqlgqWlpTBo0CAhICBA2Ldvn1BSUtLia1e5ceNGg/eyscfZs2e17rPDqa83dAS60XO8zBf95MujR4+EiIgIwd/fXxg4cKBgaWkpmJqaCnZ2dsLkyZOFbdu2CWVlZVr11ZEp65v+s+mI9B0v80U/+SKXy4V33nlHmDp1qtC/f3/BwsJCMDMzE/r27SvMnj1biImJEeo727/NjVAqO9dr0He8zBf9/T6m8pe//EUAIPj5+el8LRG1jtafwdsJRwxRAwavVhIREREREREZCUN/BucaQ0RERERERERERoqFISIiIiIiIiIiI8XCEBERERERERGRkWJhiIiIiIiIiIjISLEwRERERERERERkpFgYIiIiIiIiIiIyUiwMEREREREREREZKRaGqEmCIBg6BCIiIiIiIqIuzdCfvVkYogZMTU0BADU1NQaOhIiIiIiIiKhrq6urAwCIxWKD3J+FIWrA1NQUFhYWKCkpQX19vaHDISIiIiIiIuqSBEFAWVkZpFIpzMzMDBKDSDD0mCXqkCorK3Hr1i2IxWJYWVnB3NwcYrEYIpHI0KERERERERERdWqCIKCurg5lZWV4+PAh+vXrh549exokFhaGqEm1tbW4d+8eKisrOXKIiIiIiIiISM+kUins7OwMVhQCWBgiLagqmUql0tChEBEREREREXUJYrHYYNPHnsTCEBERERERERGRkeLi00RERERERERERoqFISIiIiIiIiIiI8XCEBERERERERGRkWJhiIiIiIiIiIjISLEwRERERERERERkpFgYIiIiIiIiIiIyUiwMEREREREREREZqf8HGUcsdqxdw1kAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1200x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "np.random.seed(42)\n", "\n", "# Set the font family to DejaVu Sans\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans']\n", "\n", "# Data from the table for training\n", "clients = ['Gateway 1', 'Gateway 2', 'Gateway 3', 'Gateway 4', 'Gateway 5', 'Gateway 6', 'Gateway 7', 'Gateway 8', 'Gateway 9', 'Gateway 10']\n", "devices = ['Device 0', '<PERSON>ce 1', 'Device 2', 'Device 3', 'Device 4', 'Device 5', 'Device 6', 'Device 7', 'Device 8']\n", "\n", "data = np.array([\n", "    [300, 158, 56, 142, 200, 531, 39, 114, 151],   # Client1\n", "    [299, 159, 53, 140, 183, 525, 41, 113, 142],   # Client2\n", "    [289, 160, 55, 140, 188, 535, 38, 118, 141],   # Client3\n", "    [285, 166, 54, 138, 185, 521, 38, 113, 153],   # Client4\n", "    [304, 156, 55, 145, 196, 498, 38, 106, 153],   # Client5\n", "    [307, 154, 54, 147, 194, 508, 40, 118, 141],   # Client6\n", "    [297, 165, 56, 141, 184, 513, 38, 122, 152],   # Client7\n", "    [293, 149, 55, 140, 187, 533, 42, 122, 155],   # Client8\n", "    [282, 159, 54, 134, 186, 536, 38, 121, 145],   # Client9\n", "    [305, 162, 54, 141, 177, 548, 41, 122, 150],   # Client10\n", "])\n", "\n", "# Plotting\n", "fig, ax = plt.subplots(figsize=(12, 10))\n", "\n", "bottom = np.zeros(len(clients))\n", "colors = plt.cm.tab20(np.linspace(0, 1, len(devices)))\n", "\n", "# Set the background color to white\n", "fig.patch.set_facecolor('white')\n", "ax.set_facecolor('white')\n", "\n", "# Draw the grid first\n", "ax.grid(True, which='both', linestyle='--', linewidth=0.5, zorder=0)\n", "\n", "for i in range(len(devices)):\n", "    ax.bar(clients, data[:, i], bottom=bottom, label=devices[i], color=colors[i], edgecolor='white', zorder=3)\n", "    bottom += data[:, i]\n", "\n", "plt.text(0.5, 0.95, 'Jensen-Shannon distance = 0.01', ha='center', va='center', transform=ax.transAxes, fontsize=18, fontweight='bold')\n", "\n", "ax.set_ylabel('No. of data samples', fontweight='bold', fontsize=18)\n", "ax.set_xlabel('IoT gateway', fontweight='bold', fontsize=18)\n", "\n", "# Create custom legend with two rows in one box\n", "handles, labels = ax.get_legend_handles_labels()\n", "ax.legend(handles, labels, title='Devices', bbox_to_anchor=(0.5, -0.18), loc='upper center', ncol=5, fontsize=16, title_fontsize=18)\n", "\n", "plt.yticks(np.arange(0, 2000, 200), fontsize=18)\n", "plt.xticks(rotation=30, fontsize=18)\n", "plt.tight_layout()\n", "plt.savefig(\"IIDdataset-training.pdf\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d954da26-c1ea-49bd-93c2-77f179b36146", "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************************/GUxf1layZ/nGKo5LZ1jSCvrv7HwHENEdRQTEyNdgqYxf3mcA1nMXxbzl8X8ZTF/*******************************************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*******************************************/PM6BLOYvi/nLYv6ymL8s5k9axvUvh40hIitSUlKkS9A05i+PcyCL+cti/rKYvyzmL4v5k5Zx/cthY4jIioyMDOkSNI35y+McyGL+spi/LOYvi/nLYv6kZVz/ctgYIrLC0dFRugRNY/7yOAeymL8s5i+L+cti/rKYP2kZ178cnaIoinQRZDuTyQSDwQCj0QgPDw/pcoiIiIiIiKqVujQORcm50mU0CY6BevjPiJQug5qg2vQKuMcQkRXR0dHSJWga85fHOZDF/GUxf1nMXxbzl8X8Scu4/uWwMUREREREREREpFFsDBFZ4efnJ12CpjF/eZwDWcxfFvOXxfxlMX9ZzJ+0jOtfDhtDRFYYDAbpEjSN+cvjHMhi/rKYvyzmL4v5y2L+pGVc/3LYGCKy4tSpU9IlaBrzl8c5kMX8ZTF/WcxfFvOXxfxJy7j+5bAxRERERERERESkUWwMEVkRHh4uXYKmMX95nANZzF8W85fF/GUxf1nMn7SM618OG0NEVly+fFm6BE1j/vI4B7KYvyzmL4v5y2L+spg/aRnXvxw2hois4JeSLOYvj3Mgi/nLYv6ymL8s5i+L+ZOWcf3LYWOIyAo7O/5qSGL+8jgHspi/LOYvi/nLYv6ymD9pGde/HJ2iKIp0EWQ7k8kEg8EAo9EIDw8P6XKIiIiIiIiqlbo0DkXJudJlNAmOgXr4z4iULoOaoNr0CtiSI7Ji37590iVoGvOXxzmQxfxlMX9ZzF8W85fF/EnLuP7lsDFEZEVpaal0CZrG/OVxDmQxf1nMXxbzl8X8ZTF/0jKufzlsDBFZ0bx5c+kSNI35y+McyGL+spi/LOYvi/nLYv6kZVz/ctgYIrKCX0qymL88zoEs5i+L+cti/rKYvyzmT1rG9S+HjSEiK06cOCFdgqYxf3mcA1nMXxbzl8X8ZTF/*******************************************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", "text/plain": ["<Figure size 1200x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "np.random.seed(42)\n", "\n", "# Set the font family to DejaVu Sans\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans']\n", "\n", "# Data from the table for training\n", "clients = ['Gateway 1', 'Gateway 2', 'Gateway 3', 'Gateway 4', 'Gateway 5', 'Gateway 6', 'Gateway 7', 'Gateway 8', 'Gateway 9', 'Gateway 10']\n", "devices = ['Device 0', '<PERSON>ce 1', 'Device 2', 'Device 3', 'Device 4', 'Device 5', 'Device 6', 'Device 7', 'Device 8']\n", "\n", "data = np.array([\n", "    [917, 0, 0, 0, 56, 39, 166, 0, 21],   # Client1\n", "    [298, 0, 0, 0, 197, 38, 0, 220, 0],   # Client2\n", "    [0, 225, 88, 0, 0, 0, 0, 0, 0],       # Client3\n", "    [92, 285, 0, 219, 0, 0, 0, 616, 760], # Client4\n", "    [586, 0, 0, 0, 239, 1235, 0, 0, 0],   # Client5\n", "    [27, 29, 0, 182, 266, 17, 154, 275, 39], # Client6\n", "    [116, 0, 366, 986, 0, 0, 72, 57, 38], # Client7\n", "    [514, 1002, 67, 0, 0, 464, 75, 0, 0], # Client8\n", "    [708, 0, 14, 0, 348, 3213, 0, 0, 0],  # Client9\n", "    [0, 41, 0, 20, 763, 234, 0, 0, 326]   # Client10\n", "])\n", "\n", "# Plotting\n", "fig, ax = plt.subplots(figsize=(12, 10))\n", "\n", "bottom = np.zeros(len(clients))\n", "colors = plt.cm.tab20(np.linspace(0, 1, len(devices)))\n", "\n", "# Set the background color to white\n", "fig.patch.set_facecolor('white')\n", "ax.set_facecolor('white')\n", "\n", "# Draw the grid first\n", "ax.grid(True, which='both', linestyle='--', linewidth=0.5, zorder=0)\n", "\n", "for i in range(len(devices)):\n", "    ax.bar(clients, data[:, i], bottom=bottom, label=devices[i], color=colors[i], edgecolor='white', zorder=3)\n", "    bottom += data[:, i]\n", "\n", "plt.text(0.5, 0.95, 'Jensen-Shannon distance = 0.83', ha='center', va='center', transform=ax.transAxes, fontsize=18, fontweight='bold')\n", "\n", "ax.set_ylabel('No. of data samples', fontweight='bold', fontsize=18)\n", "ax.set_xlabel('IoT gateway', fontweight='bold', fontsize=18)\n", "\n", "# Create custom legend with two rows in one box\n", "handles, labels = ax.get_legend_handles_labels()\n", "ax.legend(handles, labels, title='Devices', bbox_to_anchor=(0.5, -0.18), loc='upper center', ncol=5, fontsize=16, title_fontsize=18)\n", "\n", "plt.yticks(np.arange(0, 5000, 500), fontsize=18)\n", "plt.xticks(rotation=30, fontsize=18)\n", "plt.tight_layout()\n", "# plt.savefig(\"non-IIDdataset.pdf\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "43d0d0c4-742d-4137-b061-e08713725bfb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 5}