{"data_path": "../Data/N-BaIoT/IID-50-Client_Data", "devices_list": [{"id": 1, "name": "NBa-Scen2-Client-1", "normal_data_path": "Client-1/normal", "abnormal_data_path": "Client-1/abnormal", "test_normal_data_path": "Client-1/test_normal"}, {"id": 2, "name": "NBa-Scen2-Client-2", "normal_data_path": "Client-2/normal", "abnormal_data_path": "Client-2/abnormal", "test_normal_data_path": "Client-2/test_normal"}, {"id": 3, "name": "NBa-Scen2-Client-3", "normal_data_path": "Client-3/normal", "abnormal_data_path": "Client-3/abnormal", "test_normal_data_path": "Client-3/test_normal"}, {"id": 4, "name": "NBa-Scen2-Client-4", "normal_data_path": "Client-4/normal", "abnormal_data_path": "Client-4/abnormal", "test_normal_data_path": "Client-4/test_normal"}, {"id": 5, "name": "NBa-Scen2-Client-5", "normal_data_path": "Client-5/normal", "abnormal_data_path": "Client-5/abnormal", "test_normal_data_path": "Client-5/test_normal"}, {"id": 6, "name": "NBa-Scen2-Client-6", "normal_data_path": "Client-6/normal", "abnormal_data_path": "Client-6/abnormal", "test_normal_data_path": "Client-6/test_normal"}, {"id": 7, "name": "NBa-Scen2-Client-7", "normal_data_path": "Client-7/normal", "abnormal_data_path": "Client-7/abnormal", "test_normal_data_path": "Client-7/test_normal"}, {"id": 8, "name": "NBa-Scen2-Client-8", "normal_data_path": "Client-8/normal", "abnormal_data_path": "Client-8/abnormal", "test_normal_data_path": "Client-8/test_normal"}, {"id": 9, "name": "NBa-Scen2-Client-9", "normal_data_path": "Client-9/normal", "abnormal_data_path": "Client-9/abnormal", "test_normal_data_path": "Client-9/test_normal"}, {"id": 10, "name": "NBa-Scen2-Client-10", "normal_data_path": "Client-10/normal", "abnormal_data_path": "Client-10/abnormal", "test_normal_data_path": "Client-10/test_normal"}, {"id": 11, "name": "NBa-Scen2-Client-11", "normal_data_path": "Client-11/normal", "abnormal_data_path": "Client-11/abnormal", "test_normal_data_path": "Client-11/test_normal"}, {"id": 12, "name": "NBa-Scen2-Client-12", "normal_data_path": "Client-12/normal", "abnormal_data_path": "Client-12/abnormal", "test_normal_data_path": "Client-12/test_normal"}, {"id": 13, "name": "NBa-Scen2-Client-13", "normal_data_path": "Client-13/normal", "abnormal_data_path": "Client-13/abnormal", "test_normal_data_path": "Client-13/test_normal"}, {"id": 14, "name": "NBa-Scen2-Client-14", "normal_data_path": "Client-14/normal", "abnormal_data_path": "Client-14/abnormal", "test_normal_data_path": "Client-14/test_normal"}, {"id": 15, "name": "NBa-Scen2-Client-15", "normal_data_path": "Client-15/normal", "abnormal_data_path": "Client-15/abnormal", "test_normal_data_path": "Client-15/test_normal"}, {"id": 16, "name": "NBa-Scen2-Client-16", "normal_data_path": "Client-16/normal", "abnormal_data_path": "Client-16/abnormal", "test_normal_data_path": "Client-16/test_normal"}, {"id": 17, "name": "NBa-Scen2-Client-17", "normal_data_path": "Client-17/normal", "abnormal_data_path": "Client-17/abnormal", "test_normal_data_path": "Client-17/test_normal"}, {"id": 18, "name": "NBa-Scen2-Client-18", "normal_data_path": "Client-18/normal", "abnormal_data_path": "Client-18/abnormal", "test_normal_data_path": "Client-18/test_normal"}, {"id": 19, "name": "NBa-Scen2-Client-19", "normal_data_path": "Client-19/normal", "abnormal_data_path": "Client-19/abnormal", "test_normal_data_path": "Client-19/test_normal"}, {"id": 20, "name": "NBa-Scen2-Client-20", "normal_data_path": "Client-20/normal", "abnormal_data_path": "Client-20/abnormal", "test_normal_data_path": "Client-20/test_normal"}, {"id": 21, "name": "NBa-Scen2-Client-21", "normal_data_path": "Client-21/normal", "abnormal_data_path": "Client-21/abnormal", "test_normal_data_path": "Client-21/test_normal"}, {"id": 22, "name": "NBa-Scen22-Client-22", "normal_data_path": "Client-22/normal", "abnormal_data_path": "Client-22/abnormal", "test_normal_data_path": "Client-22/test_normal"}, {"id": 23, "name": "NBa-Scen2-Client-23", "normal_data_path": "Client-23/normal", "abnormal_data_path": "Client-23/abnormal", "test_normal_data_path": "Client-23/test_normal"}, {"id": 24, "name": "NBa-Scen2-Client-24", "normal_data_path": "Client-24/normal", "abnormal_data_path": "Client-24/abnormal", "test_normal_data_path": "Client-24/test_normal"}, {"id": 25, "name": "NBa-Scen2-Client-25", "normal_data_path": "Client-25/normal", "abnormal_data_path": "Client-25/abnormal", "test_normal_data_path": "Client-25/test_normal"}, {"id": 26, "name": "NBa-Scen2-Client-26", "normal_data_path": "Client-26/normal", "abnormal_data_path": "Client-26/abnormal", "test_normal_data_path": "Client-26/test_normal"}, {"id": 27, "name": "NBa-Scen2-Client-27", "normal_data_path": "Client-27/normal", "abnormal_data_path": "Client-27/abnormal", "test_normal_data_path": "Client-27/test_normal"}, {"id": 28, "name": "NBa-Scen2-Client-28", "normal_data_path": "Client-28/normal", "abnormal_data_path": "Client-28/abnormal", "test_normal_data_path": "Client-28/test_normal"}, {"id": 29, "name": "NBa-Scen2-Client-29", "normal_data_path": "Client-29/normal", "abnormal_data_path": "Client-29/abnormal", "test_normal_data_path": "Client-29/test_normal"}, {"id": 30, "name": "NBa-Scen2-Client-30", "normal_data_path": "Client-30/normal", "abnormal_data_path": "Client-30/abnormal", "test_normal_data_path": "Client-30/test_normal"}, {"id": 31, "name": "NBa-Scen2-Client-31", "normal_data_path": "Client-31/normal", "abnormal_data_path": "Client-31/abnormal", "test_normal_data_path": "Client-31/test_normal"}, {"id": 32, "name": "NBa-Scen2-Client-32", "normal_data_path": "Client-32/normal", "abnormal_data_path": "Client-32/abnormal", "test_normal_data_path": "Client-32/test_normal"}, {"id": 33, "name": "NBa-Scen2-Client-33", "normal_data_path": "Client-33/normal", "abnormal_data_path": "Client-33/abnormal", "test_normal_data_path": "Client-33/test_normal"}, {"id": 34, "name": "NBa-Scen2-Client-34", "normal_data_path": "Client-34/normal", "abnormal_data_path": "Client-34/abnormal", "test_normal_data_path": "Client-34/test_normal"}, {"id": 35, "name": "NBa-Scen2-Client-35", "normal_data_path": "Client-35/normal", "abnormal_data_path": "Client-35/abnormal", "test_normal_data_path": "Client-35/test_normal"}, {"id": 36, "name": "NBa-Scen2-Client-36", "normal_data_path": "Client-36/normal", "abnormal_data_path": "Client-36/abnormal", "test_normal_data_path": "Client-36/test_normal"}, {"id": 37, "name": "NBa-Scen2-Client-37", "normal_data_path": "Client-37/normal", "abnormal_data_path": "Client-37/abnormal", "test_normal_data_path": "Client-37/test_normal"}, {"id": 38, "name": "NBa-Scen2-Client-38", "normal_data_path": "Client-38/normal", "abnormal_data_path": "Client-38/abnormal", "test_normal_data_path": "Client-38/test_normal"}, {"id": 39, "name": "NBa-Scen2-Client-39", "normal_data_path": "Client-39/normal", "abnormal_data_path": "Client-39/abnormal", "test_normal_data_path": "Client-39/test_normal"}, {"id": 40, "name": "NBa-Scen2-Client-40", "normal_data_path": "Client-40/normal", "abnormal_data_path": "Client-40/abnormal", "test_normal_data_path": "Client-40/test_normal"}, {"id": 41, "name": "NBa-Scen2-Client-41", "normal_data_path": "Client-41/normal", "abnormal_data_path": "Client-41/abnormal", "test_normal_data_path": "Client-41/test_normal"}, {"id": 42, "name": "NBa-Scen2-Client-42", "normal_data_path": "Client-42/normal", "abnormal_data_path": "Client-42/abnormal", "test_normal_data_path": "Client-42/test_normal"}, {"id": 43, "name": "NBa-Scen2-Client-43", "normal_data_path": "Client-43/normal", "abnormal_data_path": "Client-43/abnormal", "test_normal_data_path": "Client-43/test_normal"}, {"id": 44, "name": "NBa-Scen2-Client-44", "normal_data_path": "Client-44/normal", "abnormal_data_path": "Client-44/abnormal", "test_normal_data_path": "Client-44/test_normal"}, {"id": 45, "name": "NBa-Scen2-Client-45", "normal_data_path": "Client-45/normal", "abnormal_data_path": "Client-45/abnormal", "test_normal_data_path": "Client-45/test_normal"}, {"id": 46, "name": "NBa-Scen2-Client-46", "normal_data_path": "Client-46/normal", "abnormal_data_path": "Client-46/abnormal", "test_normal_data_path": "Client-46/test_normal"}, {"id": 47, "name": "NBa-Scen2-Client-47", "normal_data_path": "Client-47/normal", "abnormal_data_path": "Client-47/abnormal", "test_normal_data_path": "Client-47/test_normal"}, {"id": 48, "name": "NBa-Scen2-Client-48", "normal_data_path": "Client-48/normal", "abnormal_data_path": "Client-48/abnormal", "test_normal_data_path": "Client-48/test_normal"}, {"id": 49, "name": "NBa-Scen2-Client-49", "normal_data_path": "Client-49/normal", "abnormal_data_path": "Client-49/abnormal", "test_normal_data_path": "Client-49/test_normal"}, {"id": 50, "name": "NBa-Scen2-Client-50", "normal_data_path": "Client-50/normal", "abnormal_data_path": "Client-50/abnormal", "test_normal_data_path": "Client-50/test_normal"}]}